# Type System Documentation

## Overview

The EliteDecor Client Approval App uses a comprehensive JSDoc type system to provide type safety and improved developer experience without requiring TypeScript. This document outlines the core types used throughout the application.

## Core Types

### Post Data Types

```javascript
/**
 * Post content data structure
 * @typedef {Object} PostContent
 * @property {string} title - Post title in Portuguese
 * @property {string} caption - Post description/content
 * @property {string} hashtags - Space-separated hashtags
 */

/**
 * Post metadata structure
 * @typedef {Object} PostMetadata
 * @property {string} id - Unique identifier
 * @property {string} image - Image URL or import
 * @property {string} category - Style category identifier
 * @property {PostContent} originalContent - Original unmodified content
 */

/**
 * Post state information
 * @typedef {Object} PostState
 * @property {boolean} isFavorited - Whether post is marked as favorite
 * @property {string} notes - Client notes for the post
 * @property {boolean} isEdited - Whether post content has been modified
 * @property {Partial<PostContent>} editedContent - Modified content fields
 * @property {ApprovalStatus} approvalStatus - Post approval status (pending, approved, declined)
 * @property {Date} lastModified - Last modification timestamp
 */

/**
 * Enhanced post with approval functionality
 * @typedef {PostMetadata & PostState} EnhancedPost
 * @property {PostContent} displayContent - Computed property merging original and edited content
 * @property {boolean} hasNotes - Whether post has notes
 * @property {boolean} hasChanges - Whether post has any modifications
 * @property {boolean} isReviewed - Whether post has been approved or declined
 */
```

### State Management Types

```javascript
/**
 * Approval status enum for posts
 * @typedef {'pending' | 'approved' | 'declined'} ApprovalStatus
 */

/**
 * Filter types for post filtering
 * @typedef {'all' | 'favorited' | 'with-notes' | 'unreviewed' | 'approved' | 'declined'} FilterType
 */

/**
 * View mode types
 * @typedef {'client' | 'admin'} ViewModeType
 */

/**
 * UI state information
 * @typedef {Object} UIState
 * @property {string|null} editingPostId - ID of post being edited, or null
 * @property {FilterType} filter - Current filter selection
 * @property {boolean} isLoading - Loading state indicator
 * @property {Date|null} lastSaved - Last save timestamp
 * @property {ViewModeType} viewMode - Current view mode
 * @property {boolean} isAdminAuthenticated - Admin authentication state
 * @property {boolean} showPasscodeDialog - Whether passcode dialog is visible
 */

/**
 * Summary statistics
 * @typedef {Object} SummaryData
 * @property {number} totalPosts - Total number of posts
 * @property {number} favoritedCount - Number of favorited posts
 * @property {number} notesCount - Number of posts with notes
 * @property {number} editedCount - Number of edited posts
 */

/**
 * Global application state
 * @typedef {Object} AppState
 * @property {Record<string, EnhancedPost>} posts - Posts indexed by ID
 * @property {UIState} ui - UI state information
 * @property {SummaryData} summary - Summary statistics
 */
```

### Action Types

```javascript
/**
 * Action types for reducer
 * @typedef {'UPDATE_POST' | 'TOGGLE_FAVORITE' | 'UPDATE_NOTES' |
 *           'SET_FILTER' | 'SET_EDITING' | 'SET_VIEW_MODE' |
 *           'SET_ADMIN_AUTH' | 'TOGGLE_PASSCODE_DIALOG' |
 *           'LOAD_STATE'} ActionType
 */

/**
 * Action object for reducer
 * @typedef {Object} Action
 * @property {ActionType} type - Action type
 * @property {any} payload - Action payload
 */

/**
 * Context actions for approval workflow
 * @typedef {Object} ApprovalActions
 * @property {(id: string, updates: Partial<PostContent>) => void} updatePost - Update post content
 * @property {(id: string) => void} toggleFavorite - Toggle favorite status
 * @property {(id: string, notes: string) => void} updateNotes - Update post notes
 * @property {(filter: FilterType) => void} setFilter - Set current filter
 * @property {(postId: string | null) => void} setEditing - Set post being edited
 * @property {(mode: ViewModeType) => void} setViewMode - Set view mode
 * @property {(isAuthenticated: boolean) => void} setAdminAuth - Set admin authentication
 * @property {(show: boolean) => void} togglePasscodeDialog - Toggle passcode dialog
 */
```

## Component Props Types

### EditableInput Props

```javascript
/**
 * EditableInput component props
 * @typedef {Object} EditableInputProps
 * @property {string} value - Current display value
 * @property {(newValue: string) => void} onSave - Callback when value is saved
 * @property {string} [placeholder] - Placeholder text for empty values
 * @property {string} [className] - Additional CSS classes
 * @property {boolean} [disabled] - Disable editing functionality
 * @property {number} [maxLength] - Maximum character limit
 */
```

### EditableTextarea Props

```javascript
/**
 * EditableTextarea component props
 * @typedef {Object} EditableTextareaProps
 * @property {string} value - Current display value
 * @property {(newValue: string) => void} onSave - Callback when value is saved
 * @property {string} [placeholder] - Placeholder text for empty values
 * @property {string} [className] - Additional CSS classes
 * @property {boolean} [disabled] - Disable editing functionality
 * @property {number} [maxLength] - Maximum character limit
 * @property {number} [minRows] - Minimum number of rows
 * @property {number} [maxRows] - Maximum number of rows for auto-resize
 */
```

### NotesDialog Props

```javascript
/**
 * NotesDialog component props
 * @typedef {Object} NotesDialogProps
 * @property {boolean} open - Whether dialog is open
 * @property {(open: boolean) => void} onOpenChange - Callback when dialog open state changes
 * @property {string} notes - Current notes content
 * @property {(notes: string) => void} onSave - Callback when notes are saved
 * @property {string} [postTitle] - Post title for context in dialog description
 */
```

### PostActions Props

```javascript
/**
 * PostActions component props
 * @typedef {Object} PostActionsProps
 * @property {EnhancedPost} post - Post object with favorite and notes data
 * @property {(postId: string) => void} onToggleFavorite - Callback for favorite toggle
 * @property {(postId: string, notes: string) => void} onUpdateNotes - Callback for notes updates
 * @property {boolean} [disabled] - Boolean to disable all interactions
 */
```

### EnhancedPostCard Props

```javascript
/**
 * EnhancedPostCard component props
 * @typedef {Object} EnhancedPostCardProps
 * @property {EnhancedPost} post - Enhanced post object with approval metadata
 * @property {boolean} [isLoading] - Whether the post is in loading state
 */
```

### ApprovalSummary Props

```javascript
/**
 * ApprovalSummary component props
 * @typedef {Object} ApprovalSummaryProps
 * No specific props as it uses the ApprovalContext directly
 */
```

## Type Usage

The type system is used throughout the application to provide:

1. **Intellisense Support**: Autocomplete and property suggestions in IDEs
2. **Documentation**: Self-documenting code with clear type definitions
3. **Error Prevention**: Catch type errors during development
4. **Refactoring Support**: Safer code refactoring with type checking

## Type Validation

While JSDoc types don't provide runtime type checking, the application includes validation functions in `postUtils.js` to ensure data integrity:

```javascript
/**
 * Validates post content against constraints
 * @param {Partial<PostContent>} content - Content to validate
 * @returns {Object} Validation result with errors if any
 */
export function validatePostContent(content) {
  const errors = {};

  // Title validation
  if (content.title !== undefined) {
    if (content.title.length > 100) {
      errors.title = "O título não pode exceder 100 caracteres";
    }
  }

  // Caption validation
  if (content.caption !== undefined) {
    if (content.caption.length > 2200) {
      errors.caption =
        "A descrição não pode exceder 2200 caracteres (limite do Instagram)";
    }
  }

  // Hashtags validation
  if (content.hashtags !== undefined) {
    if (content.hashtags.length > 200) {
      errors.hashtags = "As hashtags não podem exceder 200 caracteres";
    }

    // Check hashtag format
    const hashtags = content.hashtags.split(" ");
    if (hashtags.length > 30) {
      errors.hashtags = "O Instagram permite no máximo 30 hashtags por post";
    }

    // Validate hashtag format
    const invalidHashtags = hashtags.filter(
      (tag) => tag && !tag.startsWith("#")
    );
    if (invalidHashtags.length > 0) {
      errors.hashtags = `Hashtags devem começar com # (${invalidHashtags.join(
        ", "
      )})`;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}
```

## Future Enhancements

1. **TypeScript Migration**: Potential future migration to full TypeScript
2. **Runtime Type Checking**: Add runtime validation for critical data paths
3. **API Type Definitions**: Add types for external API integrations
4. **Prop Types Integration**: Add React PropTypes for runtime validation
