{"enabled": true, "name": "Source to Docs Sync", "description": "Monitors all source files in the React project and triggers documentation updates when changes are detected", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.jsx", "src/**/*.js", "src/**/*.css", "package.json", "vite.config.js", "index.html"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this React project. Please review the changes and update the documentation accordingly. Focus on updating the README.md file and any relevant documentation in the /docs folder to reflect the current state of the codebase, including any new features, components, data structures, or architectural changes. Ensure the documentation accurately represents the project's current functionality and structure."}}