# Project Structure & Organization

## Directory Layout

```
elitedecor-plan/
├── public/                     # Static assets served directly
├── src/                        # Source code
│   ├── components/             # React components
│   ├── data/                   # Data files and content
│   ├── assets/                 # Images and media files
│   ├── styles/                 # CSS stylesheets
│   └── main.jsx               # Application entry point
├── docs/                       # Documentation and content
└── .kiro/                     # Kiro configuration and steering
```

## Source Code Organization

### Components (`src/components/`)

- **App.jsx**: Main application component with layout and data rendering
- **Post.jsx**: Individual post component (currently unused, App renders directly)
- Follow single responsibility principle
- Use functional components with hooks

### Data Layer (`src/data/`)

- **posts.js**: Central data file containing all post content and image imports
- Organized by style categories (original, escandinavo, industrial, tropical, minimalist-v2)
- Each post object contains: id, title, image, caption, hashtags, category

### Assets (`src/assets/`)

- **posts/**: Original post images (post1-7.png)
- **styles/**: Style-specific image collections
  - `escandinavo/`: Scandinavian style images
  - `industrial/`: Industrial style images
  - `tropical/`: Tropical style images
  - `v2/`: Minimalist v2 style images
- All images are PNG format with consistent naming conventions

### Styling (`src/styles/`)

- **App.css**: Main stylesheet with Tailwind imports and custom CSS
- Combines Tailwind utility classes with custom component styles
- Responsive grid layout for post display

## Content Organization

### Documentation (`docs/`)

- **instagram-posts/**: Content planning and documentation
  - `original/`: Original post content and planning
  - `styles/`: Style-specific content by category
- Markdown files for content planning and copywriting

## Naming Conventions

### Files

- React components: PascalCase (App.jsx, Post.jsx)
- Data files: camelCase (posts.js)
- Stylesheets: kebab-case (App.css)
- Images: snake_case with category prefix (escandinavo_1.png)

### Code

- Variables and functions: camelCase
- Components: PascalCase
- Constants: UPPER_SNAKE_CASE
- CSS classes: kebab-case

## Data Structure Patterns

### Post Object Schema

```javascript
{
  id: number,           // Unique identifier
  title: string,        // Post title in Portuguese
  image: import,        // ES module import of image
  caption: string,      // Post description/content
  hashtags: string,     // Space-separated hashtags
  category: string      // Style category identifier
}
```

### Style Categories

- `original`: Classic luxury furniture posts
- `escandinavo`: Scandinavian minimalist style
- `industrial`: Urban industrial aesthetic
- `tropical`: Modern tropical luxury
- `minimalist-v2`: Cold minimalist concrete style
