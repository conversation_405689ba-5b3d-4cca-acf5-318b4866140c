# Git Commit Conventions

## Commit Message Format

Follow the Conventional Commits specification for all commits:

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

## Commit Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, etc)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools

## Scope Examples

- **auth**: Authentication related changes
- **ui**: User interface components
- **state**: State management changes
- **api**: API related changes
- **config**: Configuration changes
- **deps**: Dependency updates

## Task Completion Commits

When completing a task from the implementation plan:

1. **Complete the task implementation**
2. **Test the functionality**
3. **Commit with descriptive message following convention**
4. **Mark task as completed in tasks.md**

## Example Commit Messages

```bash
# Feature implementation
feat(auth): add 4-digit passcode authentication for admin access

# Bug fix
fix(ui): resolve post card border styling issue on mobile

# Component creation
feat(components): create EditableInput component with inline editing

# State management
feat(state): implement approval context with localStorage persistence

# UI improvements
style(ui): apply Google-inspired minimalist design system

# Testing
test(auth): add unit tests for passcode validation

# Configuration
chore(deps): install and configure shadcn/ui components
```

## Branch Strategy

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Feature branches (e.g., feature/admin-dashboard)
- **fix/**: Bug fix branches

## Commit Frequency

- Commit after completing each sub-task
- Commit working code frequently (at least daily)
- Never commit broken code to main branch
- Use meaningful commit messages that explain the "why" not just the "what"

## Pre-commit Checklist

- [ ] Code follows project conventions
- [ ] All tests pass
- [ ] No console errors or warnings
- [ ] Code is properly formatted (ESLint)
- [ ] Commit message follows convention
- [ ] Task is marked as completed in tasks.md
