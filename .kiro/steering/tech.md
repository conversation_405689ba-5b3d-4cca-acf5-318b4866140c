# Technology Stack & Build System

## Core Technologies

- **Frontend Framework**: React 18.3.1
- **Build Tool**: Vite 5.4.10
- **Styling**: Tailwind CSS 4.0.0-alpha.26 with custom CSS
- **Language**: JavaScript (ES Modules)
- **Package Manager**: npm

## Development Dependencies

- **Linting**: ESLint 9.13.0 with React plugins
- **React Tooling**: @vitejs/plugin-react for fast refresh
- **Type Support**: @types/react and @types/react-dom for better IDE support

## Build Configuration

- **Vite Config**: Custom alias setup (`@` → `./src`)
- **Server**: Development server on port 5173 with HMR
- **Tailwind**: Integrated via @tailwindcss/vite plugin
- **Module Type**: ES Modules (`"type": "module"`)

## Common Commands

### Development

```bash
npm install          # Install dependencies
npm run dev         # Start development server (http://localhost:5173)
npm run preview     # Preview production build locally
```

### Build & Quality

```bash
npm run build       # Build for production (outputs to dist/)
npm run lint        # Run ESLint for code quality
```

## Code Style Conventions

- Use ES6+ features and ES modules
- Prefer functional components with hooks
- Use JSX file extension for React components
- Import images as modules for proper bundling
- Follow ESLint configuration for consistent formatting

## Asset Management

- Images imported as ES modules in data files
- Static assets served from `public/` directory
- Organized asset structure by style categories
- PNG format for all post images
