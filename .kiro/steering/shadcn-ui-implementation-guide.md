# shadcn/ui Implementation Guide - Agent Steering

## Overview

This guide provides comprehensive instructions for implementing shadcn/ui components using the shadcn MCP (Model Context Protocol) tools. Follow these guidelines to ensure consistent, validated, and optimal shadcn/ui component implementation across all projects.

## Available MCP Commands

### 1. Component Discovery & Validation

- `mcp_shadcn_ui_list_components` - Get all 46 available components
- `mcp_shadcn_ui_get_component` - Get official component source code
- `mcp_shadcn_ui_get_component_metadata` - Get dependencies and registry info
- `mcp_shadcn_ui_get_component_demo` - Get usage examples and demos

### 2. Block Templates & Patterns

- `mcp_shadcn_ui_list_blocks` - Get 55+ pre-built blocks (calendar, dashboard, login, sidebar, products)
- `mcp_shadcn_ui_get_block` - Get complete block implementations with components

### 3. Repository Structure

- `mcp_shadcn_ui_get_directory_structure` - Explore shadcn/ui repository structure

## Implementation Workflow

### Phase 1: Pre-Implementation Analysis

**ALWAYS start with component discovery and validation:**

```javascript
// 1. List all available components to understand options
await mcp_shadcn_ui_list_components();

// 2. Get component metadata to understand dependencies
await mcp_shadcn_ui_get_component_metadata({
  componentName: "target-component",
});

// 3. Get official implementation for reference
await mcp_shadcn_ui_get_component({ componentName: "target-component" });

// 4. Get demo/usage examples
await mcp_shadcn_ui_get_component_demo({ componentName: "target-component" });
```

### Phase 2: Implementation Strategy

**Choose the right approach based on complexity:**

#### Simple Components (Button, Input, Badge, etc.)

- Use `get_component` for direct implementation
- Validate against official source
- Adapt TypeScript to JavaScript if needed

#### Complex Patterns (Dashboard, Login, Sidebar)

- Use `list_blocks` to find relevant patterns
- Use `get_block` for complete implementations
- Extract and adapt components as needed

#### Custom Implementations

- Start with closest official component
- Use `get_component_demo` for usage patterns
- Maintain shadcn/ui design principles

### Phase 3: Validation & Quality Assurance

**MANDATORY validation steps:**

1. **Source Validation**: Compare implementation against official `get_component` output
2. **Dependency Check**: Verify all dependencies from `get_component_metadata`
3. **Demo Alignment**: Ensure usage matches `get_component_demo` patterns
4. **Integration Testing**: Test component in actual application context

## Component Categories & Best Practices

### Form Components

**Components**: Input, Textarea, Select, Checkbox, Radio Group, Switch, Label, Form
**Best Practices**:

- Always validate against official implementations
- Ensure proper form integration patterns
- Include accessibility attributes
- Test with form libraries (React Hook Form, etc.)

### Navigation Components

**Components**: Navigation Menu, Breadcrumb, Pagination, Command, Menubar
**Best Practices**:

- Use blocks for complex navigation patterns
- Check sidebar blocks for advanced navigation
- Ensure keyboard navigation support

### Overlay Components

**Components**: Dialog, Sheet, Popover, Tooltip, Hover Card, Alert Dialog, Drawer
**Best Practices**:

- Verify Radix UI integration is correct
- Test portal rendering and z-index stacking
- Ensure proper focus management

### Data Display

**Components**: Table, Card, Badge, Avatar, Separator, Skeleton
**Best Practices**:

- Use dashboard blocks for complex data layouts
- Ensure responsive design patterns
- Test with various data states (loading, empty, error)

### Feedback Components

**Components**: Alert, Progress, Sonner (Toast), Loading states
**Best Practices**:

- Implement consistent feedback patterns
- Test accessibility for screen readers
- Ensure proper timing and animations

## Advanced Implementation Patterns

### Using Blocks for Complex Features

**Dashboard Implementation:**

```javascript
// 1. Explore available dashboard blocks
await mcp_shadcn_ui_list_blocks({ category: "dashboard" });

// 2. Get complete dashboard implementation
await mcp_shadcn_ui_get_block({
  blockName: "dashboard-01",
  includeComponents: true,
});

// 3. Extract and adapt components as needed
```

**Authentication Flows:**

```javascript
// 1. Check login blocks
await mcp_shadcn_ui_list_blocks({ category: "login" });

// 2. Get specific login pattern
await mcp_shadcn_ui_get_block({ blockName: "login-01" });
```

### Component Customization Strategy

1. **Start with Official**: Always begin with official component implementation
2. **Understand Dependencies**: Check metadata for required packages
3. **Adapt Gradually**: Make minimal changes to maintain compatibility
4. **Test Thoroughly**: Validate each modification against demos
5. **Document Changes**: Note any deviations from official implementation

## Quality Assurance Checklist

### Pre-Implementation

- [ ] Component exists in official registry (`list_components`)
- [ ] Dependencies identified (`get_component_metadata`)
- [ ] Official implementation reviewed (`get_component`)
- [ ] Usage patterns understood (`get_component_demo`)

### During Implementation

- [ ] Code matches official structure and patterns
- [ ] All dependencies properly installed
- [ ] TypeScript types adapted to JavaScript (if applicable)
- [ ] Styling classes preserved exactly
- [ ] Accessibility attributes maintained

### Post-Implementation

- [ ] Component renders without errors
- [ ] All variants and props work as expected
- [ ] Integration with other components tested
- [ ] Responsive behavior verified
- [ ] Accessibility tested (keyboard navigation, screen readers)

## Common Pitfalls & Solutions

### Dependency Issues

**Problem**: Missing or incorrect Radix UI dependencies
**Solution**: Always check `get_component_metadata` for exact dependency list

### Styling Inconsistencies

**Problem**: Component doesn't match shadcn/ui design
**Solution**: Compare className usage with official `get_component` output

### TypeScript to JavaScript Conversion

**Problem**: Type errors when adapting TypeScript components
**Solution**: Remove type annotations but preserve all functionality and prop interfaces

### Integration Problems

**Problem**: Component doesn't work with existing codebase
**Solution**: Review `get_component_demo` for proper usage patterns

## Performance Optimization

### Bundle Size Management

- Use `get_component_metadata` to understand dependency impact
- Import only needed component variants
- Consider tree-shaking implications

### Loading Strategies

- Implement skeleton states for data components
- Use progressive enhancement patterns
- Consider lazy loading for complex blocks

## Accessibility Standards

### Required Practices

- Preserve all ARIA attributes from official implementations
- Test keyboard navigation thoroughly
- Ensure proper focus management
- Validate screen reader compatibility
- Maintain color contrast ratios

### Testing Approach

- Use automated accessibility testing tools
- Manual keyboard navigation testing
- Screen reader testing (VoiceOver, NVDA, JAWS)
- Color blindness simulation

## Documentation Standards

### Component Documentation

- Include usage examples from `get_component_demo`
- Document any deviations from official implementation
- Provide prop interface documentation
- Include accessibility notes

### Implementation Notes

- Record shadcn MCP commands used
- Note any customizations made
- Document integration patterns
- Include troubleshooting tips

## Maintenance & Updates

### Regular Validation

- Periodically re-run `get_component` to check for updates
- Compare current implementation with latest official version
- Update dependencies as needed
- Test for breaking changes

### Version Management

- Track shadcn/ui version compatibility
- Document component version history
- Plan migration strategies for major updates

## Emergency Troubleshooting

### Component Not Working

1. Re-run `get_component` to verify official implementation
2. Check `get_component_metadata` for dependency changes
3. Compare with `get_component_demo` usage patterns
4. Validate all required props and styling

### Styling Issues

1. Compare className usage with official implementation
2. Check for Tailwind CSS configuration issues
3. Verify CSS variable definitions
4. Test in isolation to identify conflicts

### Integration Failures

1. Review `get_component_demo` for proper usage
2. Check for prop passing issues
3. Validate event handler implementations
4. Test component lifecycle behavior

## Success Metrics

### Implementation Quality

- 100% match with official component structure
- All variants and props functional
- Zero accessibility violations
- Consistent styling across browsers

### Development Efficiency

- Reduced implementation time through MCP usage
- Fewer bugs due to validation process
- Improved code consistency
- Better maintainability

## Conclusion

This guide ensures that all shadcn/ui component implementations are:

- **Validated** against official sources
- **Consistent** with shadcn/ui standards
- **Accessible** and user-friendly
- **Maintainable** and future-proof
- **Optimized** for performance

Always use the shadcn MCP tools as your primary reference and validation source. When in doubt, refer back to the official implementations and demos provided by these tools.
