/**
 * @fileoverview Tests for post utilities
 */

import { describe, it, expect, beforeEach, vi } from "vitest";
import {
  transformToEnhancedPost,
  addComputedProperties,
  mergePostContent,
  migratePostsData,
  updatePostContent,
  togglePostFavorite,
  updatePostNotes,
  filterPosts,
  calculateSummary,
  getPostStatusIndicators,
  validateTitle,
  validateCaption,
  validateHashtags,
  validatePostContent,
  sanitizePostContent,
  createVali<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  VALIDATION_LIMITS,
  VALIDATION_ERRORS,
} from "../postUtils.js";

describe("postUtils", () => {
  let mockOriginalPost;
  let mockEnhancedPost;

  beforeEach(() => {
    mockOriginalPost = {
      id: 1,
      title: "Test Title",
      image: "test-image.png",
      caption: "Test caption content",
      hashtags: "#test #hashtags",
      category: "original",
    };

    mockEnhancedPost = {
      id: "1",
      image: "test-image.png",
      category: "original",
      originalContent: {
        title: "Test Title",
        caption: "Test caption content",
        hashtags: "#test #hashtags",
      },
      isFavorited: false,
      notes: "",
      isEdited: false,
      editedContent: {},
      lastModified: new Date("2024-01-01"),
    };
  });

  describe("transformToEnhancedPost", () => {
    it("should transform original post to enhanced post structure", () => {
      const result = transformToEnhancedPost(mockOriginalPost);

      expect(result.id).toBe("1");
      expect(result.image).toBe("test-image.png");
      expect(result.category).toBe("original");
      expect(result.originalContent).toEqual({
        title: "Test Title",
        caption: "Test caption content",
        hashtags: "#test #hashtags",
      });
      expect(result.isFavorited).toBe(false);
      expect(result.notes).toBe("");
      expect(result.isEdited).toBe(false);
      expect(result.editedContent).toEqual({});
      expect(result.lastModified).toBeInstanceOf(Date);
    });

    it("should add computed properties", () => {
      const result = transformToEnhancedPost(mockOriginalPost);

      expect(result.displayContent).toEqual({
        title: "Test Title",
        caption: "Test caption content",
        hashtags: "#test #hashtags",
      });
      expect(result.hasNotes).toBe(false);
      expect(result.hasChanges).toBe(false);
    });
  });

  describe("addComputedProperties", () => {
    it("should add computed displayContent property", () => {
      const post = { ...mockEnhancedPost };
      const result = addComputedProperties(post);

      expect(result.displayContent).toEqual({
        title: "Test Title",
        caption: "Test caption content",
        hashtags: "#test #hashtags",
      });
    });

    it("should merge edited content in displayContent", () => {
      const post = {
        ...mockEnhancedPost,
        editedContent: {
          title: "Edited Title",
          caption: "Edited caption",
        },
      };
      const result = addComputedProperties(post);

      expect(result.displayContent).toEqual({
        title: "Edited Title",
        caption: "Edited caption",
        hashtags: "#test #hashtags",
      });
    });

    it("should compute hasNotes correctly", () => {
      const postWithNotes = { ...mockEnhancedPost, notes: "Some notes" };
      const postWithoutNotes = { ...mockEnhancedPost, notes: "" };

      expect(addComputedProperties(postWithNotes).hasNotes).toBe(true);
      expect(addComputedProperties(postWithoutNotes).hasNotes).toBe(false);
    });

    it("should compute hasChanges correctly", () => {
      const postWithChanges = {
        ...mockEnhancedPost,
        isEdited: true,
        editedContent: { title: "New Title" },
      };
      const postWithoutChanges = { ...mockEnhancedPost };

      expect(addComputedProperties(postWithChanges).hasChanges).toBe(true);
      expect(addComputedProperties(postWithoutChanges).hasChanges).toBe(false);
    });
  });

  describe("mergePostContent", () => {
    it("should merge original and edited content", () => {
      const originalContent = {
        title: "Original Title",
        caption: "Original caption",
        hashtags: "#original",
      };
      const editedContent = {
        title: "Edited Title",
        caption: "Edited caption",
      };

      const result = mergePostContent(originalContent, editedContent);

      expect(result).toEqual({
        title: "Edited Title",
        caption: "Edited caption",
        hashtags: "#original",
      });
    });

    it("should use original content when no edits provided", () => {
      const originalContent = {
        title: "Original Title",
        caption: "Original caption",
        hashtags: "#original",
      };

      const result = mergePostContent(originalContent);

      expect(result).toEqual(originalContent);
    });
  });

  describe("migratePostsData", () => {
    it("should migrate array of posts to indexed object", () => {
      const originalPosts = [
        mockOriginalPost,
        { ...mockOriginalPost, id: 2, title: "Post 2" },
      ];

      const result = migratePostsData(originalPosts);

      expect(Object.keys(result)).toEqual(["1", "2"]);
      expect(result["1"].originalContent.title).toBe("Test Title");
      expect(result["2"].originalContent.title).toBe("Post 2");
    });
  });

  describe("updatePostContent", () => {
    it("should update post content and mark as edited", () => {
      const post = addComputedProperties(mockEnhancedPost);
      const updates = { title: "Updated Title", caption: "Updated caption" };

      const result = updatePostContent(post, updates);

      expect(result.editedContent).toEqual(updates);
      expect(result.isEdited).toBe(true);
      expect(result.lastModified).toBeInstanceOf(Date);
      expect(result.displayContent.title).toBe("Updated Title");
      expect(result.displayContent.caption).toBe("Updated caption");
    });
  });

  describe("togglePostFavorite", () => {
    it("should toggle favorite status", () => {
      const post = addComputedProperties(mockEnhancedPost);

      const favorited = togglePostFavorite(post);
      expect(favorited.isFavorited).toBe(true);

      const unfavorited = togglePostFavorite(favorited);
      expect(unfavorited.isFavorited).toBe(false);
    });
  });

  describe("updatePostNotes", () => {
    it("should update post notes", () => {
      const post = addComputedProperties(mockEnhancedPost);
      const notes = "These are my notes";

      const result = updatePostNotes(post, notes);

      expect(result.notes).toBe(notes);
      expect(result.hasNotes).toBe(true);
      expect(result.lastModified).toBeInstanceOf(Date);
    });

    it("should handle empty notes", () => {
      const post = addComputedProperties({
        ...mockEnhancedPost,
        notes: "existing notes",
      });

      const result = updatePostNotes(post, "");

      expect(result.notes).toBe("");
      expect(result.hasNotes).toBe(false);
    });
  });

  describe("filterPosts", () => {
    let posts;

    beforeEach(() => {
      posts = {
        1: addComputedProperties({
          ...mockEnhancedPost,
          id: "1",
          isFavorited: true,
        }),
        2: addComputedProperties({
          ...mockEnhancedPost,
          id: "2",
          notes: "Has notes",
        }),
        3: addComputedProperties({
          ...mockEnhancedPost,
          id: "3",
          isEdited: true,
          editedContent: { title: "Edited" },
        }),
        4: addComputedProperties({ ...mockEnhancedPost, id: "4" }),
      };
    });

    it("should filter favorited posts", () => {
      const result = filterPosts(posts, "favorited");
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe("1");
    });

    it("should filter posts with notes", () => {
      const result = filterPosts(posts, "with-notes");
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe("2");
    });

    it("should filter unreviewed posts", () => {
      const result = filterPosts(posts, "unreviewed");
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe("4");
    });

    it('should return all posts for "all" filter', () => {
      const result = filterPosts(posts, "all");
      expect(result).toHaveLength(4);
    });
  });

  describe("calculateSummary", () => {
    it("should calculate correct summary statistics", () => {
      const posts = {
        1: addComputedProperties({
          ...mockEnhancedPost,
          id: "1",
          isFavorited: true,
        }),
        2: addComputedProperties({
          ...mockEnhancedPost,
          id: "2",
          notes: "Has notes",
        }),
        3: addComputedProperties({
          ...mockEnhancedPost,
          id: "3",
          isEdited: true,
          editedContent: { title: "Edited" },
        }),
        4: addComputedProperties({ ...mockEnhancedPost, id: "4" }),
      };

      const result = calculateSummary(posts);

      expect(result).toEqual({
        totalPosts: 4,
        favoritedCount: 1,
        notesCount: 1,
        editedCount: 1,
        approvedCount: 0,
        declinedCount: 0,
        reviewedCount: 0,
      });
    });
  });

  describe("getPostStatusIndicators", () => {
    it("should return correct status indicators", () => {
      const post = addComputedProperties({
        ...mockEnhancedPost,
        isFavorited: true,
        notes: "Some notes",
        isEdited: true,
        editedContent: { title: "Edited" },
      });

      const result = getPostStatusIndicators(post);

      expect(result).toEqual({
        isFavorited: true,
        hasNotes: true,
        hasChanges: true,
        isEdited: true,
        needsReview: false,
      });
    });

    it("should identify posts that need review", () => {
      const post = addComputedProperties(mockEnhancedPost);

      const result = getPostStatusIndicators(post);

      expect(result.needsReview).toBe(true);
    });
  });

  describe("Validation Functions", () => {
    describe("validateTitle", () => {
      it("should validate valid titles", () => {
        const result = validateTitle("Valid Title");
        expect(result.isValid).toBe(true);
        expect(result.error).toBe(null);
      });

      it("should reject empty titles", () => {
        const result = validateTitle("");
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("required");
        expect(result.message).toBe("O título é obrigatório");
      });

      it("should reject null/undefined titles", () => {
        expect(validateTitle(null).isValid).toBe(false);
        expect(validateTitle(undefined).isValid).toBe(false);
      });

      it("should reject titles that are too long", () => {
        const longTitle = "a".repeat(101);
        const result = validateTitle(longTitle);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("too_long");
      });

      it("should trim whitespace and validate", () => {
        const result = validateTitle("  Valid Title  ");
        expect(result.isValid).toBe(true);
      });
    });

    describe("validateCaption", () => {
      it("should validate valid captions", () => {
        const result = validateCaption("This is a valid caption");
        expect(result.isValid).toBe(true);
        expect(result.error).toBe(null);
      });

      it("should reject empty captions", () => {
        const result = validateCaption("");
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("required");
        expect(result.message).toBe("A descrição é obrigatória");
      });

      it("should reject captions that are too long", () => {
        const longCaption = "a".repeat(2201);
        const result = validateCaption(longCaption);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("too_long");
      });

      it("should accept captions up to the limit", () => {
        const maxCaption = "a".repeat(2200);
        const result = validateCaption(maxCaption);
        expect(result.isValid).toBe(true);
      });
    });

    describe("validateHashtags", () => {
      it("should validate valid hashtags", () => {
        const result = validateHashtags("#test #hashtags #valid");
        expect(result.isValid).toBe(true);
        expect(result.error).toBe(null);
      });

      it("should reject empty hashtags", () => {
        const result = validateHashtags("");
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("required");
      });

      it("should reject hashtags without # symbol", () => {
        const result = validateHashtags("test hashtags");
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("invalid_format");
      });

      it("should reject hashtags that are too long", () => {
        const longHashtags = "#test ".repeat(100);
        const result = validateHashtags(longHashtags);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("too_long");
      });

      it("should reject too many hashtags", () => {
        const manyHashtags = Array.from(
          { length: 31 },
          (_, i) => `#tag${i}`
        ).join(" ");
        const result = validateHashtags(manyHashtags);
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("too_many_hashtags");
      });

      it("should reject hashtags with invalid characters", () => {
        const result = validateHashtags("#test-invalid #valid");
        expect(result.isValid).toBe(false);
        expect(result.error).toBe("invalid_format");
      });

      it("should accept hashtags with underscores and numbers", () => {
        const result = validateHashtags("#test_123 #valid_hashtag");
        expect(result.isValid).toBe(true);
      });
    });

    describe("validatePostContent", () => {
      it("should validate complete valid post content", () => {
        const content = {
          title: "Valid Title",
          caption: "Valid caption content",
          hashtags: "#valid #hashtags",
        };

        const result = validatePostContent(content);
        expect(result.isValid).toBe(true);
        expect(result.errors.title).toBe(null);
        expect(result.errors.caption).toBe(null);
        expect(result.errors.hashtags).toBe(null);
      });

      it("should return field-specific errors", () => {
        const content = {
          title: "",
          caption: "Valid caption",
          hashtags: "invalid hashtags",
        };

        const result = validatePostContent(content);
        expect(result.isValid).toBe(false);
        expect(result.errors.title).not.toBe(null);
        expect(result.errors.caption).toBe(null);
        expect(result.errors.hashtags).not.toBe(null);
      });
    });

    describe("sanitizePostContent", () => {
      it("should trim whitespace from all fields", () => {
        const content = {
          title: "  Title with spaces  ",
          caption: "  Caption with spaces  ",
          hashtags: "  #hashtags #with #spaces  ",
        };

        const result = sanitizePostContent(content);
        expect(result.title).toBe("Title with spaces");
        expect(result.caption).toBe("Caption with spaces");
        expect(result.hashtags).toBe("#hashtags #with #spaces");
      });

      it("should handle empty/null values", () => {
        const content = {
          title: null,
          caption: undefined,
          hashtags: "",
        };

        const result = sanitizePostContent(content);
        expect(result.title).toBe("");
        expect(result.caption).toBe("");
        expect(result.hashtags).toBe("");
      });
    });

    describe("createValidationErrorHandler", () => {
      it("should create error handler that calls callback on validation errors", () => {
        const mockOnError = vi.fn();
        const errorHandler = createValidationErrorHandler(mockOnError);

        errorHandler("title", "");

        expect(mockOnError).toHaveBeenCalledWith(
          "title",
          expect.objectContaining({
            isValid: false,
            error: "required",
          })
        );
      });

      it("should not call callback for valid values", () => {
        const mockOnError = vi.fn();
        const errorHandler = createValidationErrorHandler(mockOnError);

        errorHandler("title", "Valid Title");

        expect(mockOnError).not.toHaveBeenCalled();
      });

      it("should handle unknown fields gracefully", () => {
        const mockOnError = vi.fn();
        const errorHandler = createValidationErrorHandler(mockOnError);

        errorHandler("unknown", "value");

        expect(mockOnError).not.toHaveBeenCalled();
      });
    });
  });
});
