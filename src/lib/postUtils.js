/**
 * @fileoverview Utilities for transforming and managing enhanced post data structure
 */

/**
 * Transforms a basic post object into an enhanced post with approval metadata
 * @param {Object} originalPost - The original post object
 * @returns {import('../types/index.js').EnhancedPost} Enhanced post with approval metadata
 */
export function transformToEnhancedPost(originalPost) {
  const enhancedPost = {
    // Metadata
    id: String(originalPost.id),
    image: originalPost.image,
    category: originalPost.category,

    // Original content structure
    originalContent: {
      title: originalPost.title,
      caption: originalPost.caption,
      hashtags: originalPost.hashtags,
    },

    // State properties with defaults
    isFavorited: originalPost.isFavorited || false,
    notes: originalPost.notes || "",
    isEdited: originalPost.isEdited || false,
    editedContent: originalPost.editedContent || {},
    approvalStatus: originalPost.approvalStatus || 'pending',
    lastModified: originalPost.lastModified || new Date(),
  };

  // Add computed properties
  return addComputedProperties(enhancedPost);
}

/**
 * Adds computed properties to an enhanced post
 * @param {Object} post - Post object to enhance
 * @returns {import('../types/index.js').EnhancedPost} Post with computed properties
 */
export function addComputedProperties(post) {
  return {
    ...post,

    // Computed displayContent: merges original with edited content
    get displayContent() {
      return {
        title: post.editedContent.title || post.originalContent.title,
        caption: post.editedContent.caption || post.originalContent.caption,
        hashtags: post.editedContent.hashtags || post.originalContent.hashtags,
      };
    },

    // Computed hasNotes: checks if notes exist
    get hasNotes() {
      return Boolean(post.notes && post.notes.trim().length > 0);
    },

    // Computed hasChanges: checks if any content has been edited
    get hasChanges() {
      return Boolean(post.isEdited);
    },

    // Computed isReviewed: checks if post has been explicitly reviewed
    get isReviewed() {
      return post.approvalStatus === 'approved' || post.approvalStatus === 'declined';
    },
  };
}

/**
 * Merges original and edited content for display
 * @param {import('../types/index.js').PostContent} originalContent - Original post content
 * @param {Partial<import('../types/index.js').PostContent>} editedContent - Edited content fields
 * @returns {import('../types/index.js').PostContent} Merged content for display
 */
export function mergePostContent(originalContent, editedContent = {}) {
  return {
    title: editedContent.title || originalContent.title,
    caption: editedContent.caption || originalContent.caption,
    hashtags: editedContent.hashtags || originalContent.hashtags,
  };
}

/**
 * Migrates existing posts array to enhanced post structure
 * @param {Array} originalPosts - Array of original post objects
 * @returns {Record<string, import('../types/index.js').EnhancedPost>} Posts indexed by ID
 */
export function migratePostsData(originalPosts) {
  const enhancedPosts = {};

  originalPosts.forEach((post) => {
    const enhanced = transformToEnhancedPost(post);
    enhancedPosts[enhanced.id] = enhanced;
  });

  return enhancedPosts;
}

/**
 * Updates a post's content and marks it as edited
 * @param {import('../types/index.js').EnhancedPost} post - Post to update
 * @param {Partial<import('../types/index.js').PostContent>} updates - Content updates
 * @returns {import('../types/index.js').EnhancedPost} Updated post
 */
export function updatePostContent(post, updates) {
  const updatedPost = {
    ...post,
    editedContent: {
      ...post.editedContent,
      ...updates,
    },
    isEdited: true,
    lastModified: new Date(),
  };

  return addComputedProperties(updatedPost);
}

/**
 * Toggles the favorite status of a post
 * @param {import('../types/index.js').EnhancedPost} post - Post to toggle
 * @returns {import('../types/index.js').EnhancedPost} Updated post
 */
export function togglePostFavorite(post) {
  const updatedPost = {
    ...post,
    isFavorited: !post.isFavorited,
    lastModified: new Date(),
  };

  return addComputedProperties(updatedPost);
}

/**
 * Updates the notes for a post
 * @param {import('../types/index.js').EnhancedPost} post - Post to update
 * @param {string} notes - New notes content
 * @returns {import('../types/index.js').EnhancedPost} Updated post
 */
export function updatePostNotes(post, notes) {
  const updatedPost = {
    ...post,
    notes: notes || "",
    lastModified: new Date(),
  };

  return addComputedProperties(updatedPost);
}

/**
 * Filters posts based on the specified filter type
 * @param {Record<string, import('../types/index.js').EnhancedPost>} posts - Posts to filter
 * @param {import('../types/index.js').FilterType} filter - Filter type
 * @returns {Array<import('../types/index.js').EnhancedPost>} Filtered posts array
 */
export function filterPosts(posts, filter) {
  // Handle null, undefined, or invalid posts input
  if (!posts || typeof posts !== "object") {
    return [];
  }

  const postsArray = Object.values(posts);

  switch (filter) {
    case "favorited":
      return postsArray.filter((post) => post.isFavorited);

    case "with-notes":
      return postsArray.filter((post) => post.hasNotes);

    case "edited":
      return postsArray.filter((post) => post.hasChanges);

    case "approved":
      return postsArray.filter((post) => post.approvalStatus === 'approved');

    case "declined":
      return postsArray.filter((post) => post.approvalStatus === 'declined');

    case "unreviewed":
      return postsArray.filter((post) =>
        !post.isFavorited &&
        !post.hasNotes &&
        !post.hasChanges
      );

    case "all":
    default:
      return postsArray;
  }
}

/**
 * Calculates summary statistics for posts
 * @param {Record<string, import('../types/index.js').EnhancedPost>} posts - Posts to analyze
 * @returns {import('../types/index.js').SummaryData} Summary statistics
 */
export function calculateSummary(posts) {
  const postsArray = Object.values(posts);

  const approvedCount = postsArray.filter((post) => post.approvalStatus === 'approved').length;
  const declinedCount = postsArray.filter((post) => post.approvalStatus === 'declined').length;

  return {
    totalPosts: postsArray.length,
    favoritedCount: postsArray.filter((post) => post.isFavorited).length,
    notesCount: postsArray.filter((post) => post.hasNotes).length,
    editedCount: postsArray.filter((post) => post.hasChanges).length,
    approvedCount,
    declinedCount,
    reviewedCount: approvedCount + declinedCount,
  };
}

/**
 * Gets status indicators for a post
 * @param {import('../types/index.js').EnhancedPost} post - Post to analyze
 * @returns {Object} Status indicators
 */
export function getPostStatusIndicators(post) {
  return {
    isFavorited: post.isFavorited,
    hasNotes: post.hasNotes,
    hasChanges: post.hasChanges,
    isEdited: post.isEdited,
    needsReview: !post.isFavorited && !post.hasNotes && !post.hasChanges,
  };
}

/**
 * Validation configuration constants
 */
export const VALIDATION_LIMITS = {
  TITLE_MIN_LENGTH: 1,
  TITLE_MAX_LENGTH: 100,
  CAPTION_MIN_LENGTH: 1,
  CAPTION_MAX_LENGTH: 2200, // Instagram caption limit
  HASHTAGS_MAX_LENGTH: 500,
  MAX_HASHTAGS: 30, // Instagram hashtag limit
};

/**
 * Validation error types
 */
export const VALIDATION_ERRORS = {
  REQUIRED: "required",
  TOO_SHORT: "too_short",
  TOO_LONG: "too_long",
  INVALID_FORMAT: "invalid_format",
  TOO_MANY_HASHTAGS: "too_many_hashtags",
};

/**
 * Validates post title
 * @param {string} title - Title to validate
 * @returns {Object} Validation result with isValid and error properties
 */
export function validateTitle(title) {
  if (!title || typeof title !== "string") {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.REQUIRED,
      message: "O título é obrigatório",
    };
  }

  const trimmedTitle = title.trim();

  if (trimmedTitle.length < VALIDATION_LIMITS.TITLE_MIN_LENGTH) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_SHORT,
      message: "O título deve ter pelo menos 1 caractere",
    };
  }

  if (trimmedTitle.length > VALIDATION_LIMITS.TITLE_MAX_LENGTH) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_LONG,
      message: `O título não pode exceder ${VALIDATION_LIMITS.TITLE_MAX_LENGTH} caracteres`,
    };
  }

  return {
    isValid: true,
    error: null,
    message: null,
  };
}

/**
 * Validates post caption/description
 * @param {string} caption - Caption to validate
 * @returns {Object} Validation result with isValid and error properties
 */
export function validateCaption(caption) {
  if (!caption || typeof caption !== "string") {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.REQUIRED,
      message: "A descrição é obrigatória",
    };
  }

  const trimmedCaption = caption.trim();

  if (trimmedCaption.length < VALIDATION_LIMITS.CAPTION_MIN_LENGTH) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_SHORT,
      message: "A descrição deve ter pelo menos 1 caractere",
    };
  }

  if (trimmedCaption.length > VALIDATION_LIMITS.CAPTION_MAX_LENGTH) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_LONG,
      message: `A descrição não pode exceder ${VALIDATION_LIMITS.CAPTION_MAX_LENGTH} caracteres`,
    };
  }

  return {
    isValid: true,
    error: null,
    message: null,
  };
}

/**
 * Validates hashtags string
 * @param {string} hashtags - Hashtags to validate
 * @returns {Object} Validation result with isValid and error properties
 */
export function validateHashtags(hashtags) {
  if (!hashtags || typeof hashtags !== "string") {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.REQUIRED,
      message: "As hashtags são obrigatórias",
    };
  }

  const trimmedHashtags = hashtags.trim();

  if (trimmedHashtags.length > VALIDATION_LIMITS.HASHTAGS_MAX_LENGTH) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_LONG,
      message: `As hashtags não podem exceder ${VALIDATION_LIMITS.HASHTAGS_MAX_LENGTH} caracteres`,
    };
  }

  // Extract hashtags and validate format
  const hashtagMatches = trimmedHashtags.match(/#[a-zA-Z0-9_-]+/g) || [];

  if (hashtagMatches.length === 0) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.INVALID_FORMAT,
      message: "Deve incluir pelo menos uma hashtag válida (ex: #exemplo)",
    };
  }

  if (hashtagMatches.length > VALIDATION_LIMITS.MAX_HASHTAGS) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.TOO_MANY_HASHTAGS,
      message: `Não pode exceder ${VALIDATION_LIMITS.MAX_HASHTAGS} hashtags`,
    };
  }

  // Check for invalid hashtag format (hashtags should only contain letters, numbers, underscores)
  const invalidHashtags = hashtagMatches.filter(
    (hashtag) => !/^#[a-zA-Z0-9_]+$/.test(hashtag)
  );

  if (invalidHashtags.length > 0) {
    return {
      isValid: false,
      error: VALIDATION_ERRORS.INVALID_FORMAT,
      message: "As hashtags devem conter apenas letras, números e sublinhados",
    };
  }

  return {
    isValid: true,
    error: null,
    message: null,
  };
}

/**
 * Validates complete post content
 * @param {import('../types/index.js').PostContent} content - Post content to validate
 * @returns {Object} Validation result with field-specific errors
 */
export function validatePostContent(content) {
  const titleValidation = validateTitle(content.title);
  const captionValidation = validateCaption(content.caption);
  const hashtagsValidation = validateHashtags(content.hashtags);

  const isValid =
    titleValidation.isValid &&
    captionValidation.isValid &&
    hashtagsValidation.isValid;

  return {
    isValid,
    errors: {
      title: titleValidation.isValid ? null : titleValidation,
      caption: captionValidation.isValid ? null : captionValidation,
      hashtags: hashtagsValidation.isValid ? null : hashtagsValidation,
    },
  };
}

/**
 * Sanitizes post content by trimming whitespace and removing potentially harmful content
 * @param {import('../types/index.js').PostContent} content - Content to sanitize
 * @returns {import('../types/index.js').PostContent} Sanitized content
 */
export function sanitizePostContent(content) {
  return {
    title: content.title ? content.title.trim() : "",
    caption: content.caption ? content.caption.trim() : "",
    hashtags: content.hashtags ? content.hashtags.trim() : "",
  };
}

/**
 * Creates a validation error handler for form fields
 * @param {Function} onError - Error callback function
 * @returns {Function} Error handler function
 */
export function createValidationErrorHandler(onError) {
  return (field, value) => {
    let validation;

    switch (field) {
      case "title":
        validation = validateTitle(value);
        break;
      case "caption":
        validation = validateCaption(value);
        break;
      case "hashtags":
        validation = validateHashtags(value);
        break;
      default:
        return;
    }

    if (!validation.isValid) {
      onError(field, validation);
    }
  };
}
