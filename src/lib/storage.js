/**
 * localStorage utilities for approval state persistence
 */

const STORAGE_KEY = "elitedecor-approval-state";
const DEBOUNCE_DELAY = 1000; // 1 second debounce

// Debounce utility function
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Serialize a post for storage by removing computed properties
 * @param {Object} post - Post to serialize
 * @returns {Object} Serializable post object
 */
function serializePost(post) {
  return {
    id: post.id,
    image: post.image,
    category: post.category,
    originalContent: post.originalContent,
    isFavorited: post.isFavorited,
    notes: post.notes,
    isEdited: post.isEdited,
    editedContent: post.editedContent,
    approvalStatus: post.approvalStatus,
    lastModified: post.lastModified
  };
}

/**
 * Save approval state to localStorage
 * @param {Object} state - The approval state to save
 */
export const saveApprovalState = (state) => {
  try {
    // Serialize posts to remove computed properties
    const serializedPosts = {};
    Object.entries(state.posts).forEach(([id, post]) => {
      serializedPosts[id] = serializePost(post);
    });

    // Only save the essential parts of the state
    const stateToSave = {
      posts: serializedPosts,
      ui: {
        filter: state.ui.filter,
        viewMode: state.ui.viewMode,
        isAdminAuthenticated: state.ui.isAdminAuthenticated,
      },
      timestamp: new Date().toISOString(),
    };

    const serializedState = JSON.stringify(stateToSave);
    localStorage.setItem(STORAGE_KEY, serializedState);

    console.log("Approval state saved to localStorage");
    return true;
  } catch (error) {
    console.error("Failed to save approval state to localStorage:", error);
    return false;
  }
};

/**
 * Load approval state from localStorage
 * @returns {Object|null} The loaded state or null if not found/invalid
 */
export const loadApprovalState = () => {
  try {
    const serializedState = localStorage.getItem(STORAGE_KEY);

    if (!serializedState) {
      console.log("No approval state found in localStorage");
      return null;
    }

    const parsedState = JSON.parse(serializedState);

    // Validate the loaded state structure
    if (!parsedState.posts || !parsedState.ui || !parsedState.timestamp) {
      console.warn("Invalid approval state structure in localStorage");
      return null;
    }

    // Convert timestamp strings back to Date objects in posts
    Object.values(parsedState.posts).forEach((post) => {
      if (post.lastModified) {
        post.lastModified = new Date(post.lastModified);
      }
    });

    console.log("Approval state loaded from localStorage");
    return parsedState;
  } catch (error) {
    console.error("Failed to load approval state from localStorage:", error);
    return null;
  }
};

/**
 * Clear approval state from localStorage
 */
export const clearApprovalState = () => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log("Approval state cleared from localStorage");
    return true;
  } catch (error) {
    console.error("Failed to clear approval state from localStorage:", error);
    return false;
  }
};

/**
 * Check if localStorage is available
 * @returns {boolean} True if localStorage is available
 */
export const isStorageAvailable = () => {
  try {
    const testKey = "__storage_test__";
    localStorage.setItem(testKey, "test");
    localStorage.removeItem(testKey);
    return true;
  } catch (error) {
    console.warn("localStorage is not available:", error);
    return false;
  }
};

/**
 * Debounced save function to prevent excessive localStorage writes
 */
export const debouncedSaveApprovalState = debounce(
  saveApprovalState,
  DEBOUNCE_DELAY
);

/**
 * Get storage statistics
 * @returns {Object} Storage usage information
 */
export const getStorageStats = () => {
  try {
    const serializedState = localStorage.getItem(STORAGE_KEY);
    const sizeInBytes = serializedState ? new Blob([serializedState]).size : 0;
    const sizeInKB = (sizeInBytes / 1024).toFixed(2);

    return {
      exists: !!serializedState,
      sizeInBytes,
      sizeInKB,
      lastSaved: serializedState ? JSON.parse(serializedState).timestamp : null,
    };
  } catch (error) {
    console.error("Failed to get storage stats:", error);
    return {
      exists: false,
      sizeInBytes: 0,
      sizeInKB: "0.00",
      lastSaved: null,
    };
  }
};

/**
 * Recovery mechanism for unsaved changes
 * Compares current state with saved state to detect unsaved changes
 * @param {Object} currentState - Current approval state
 * @returns {Object} Recovery information
 */
export const checkForUnsavedChanges = (currentState) => {
  try {
    const savedState = loadApprovalState();

    if (!savedState) {
      return {
        hasUnsavedChanges: false,
        unsavedPosts: [],
        message: "No saved state found",
      };
    }

    const unsavedPosts = [];

    // Compare each post's lastModified timestamp
    Object.values(currentState.posts).forEach((currentPost) => {
      const savedPost = savedState.posts[currentPost.id];

      if (!savedPost) {
        // New post not in saved state
        unsavedPosts.push({
          id: currentPost.id,
          reason: "new_post",
          currentModified: currentPost.lastModified,
          savedModified: null,
        });
      } else if (
        new Date(currentPost.lastModified) > new Date(savedPost.lastModified)
      ) {
        // Post modified after last save
        unsavedPosts.push({
          id: currentPost.id,
          reason: "modified_after_save",
          currentModified: currentPost.lastModified,
          savedModified: savedPost.lastModified,
        });
      }
    });

    return {
      hasUnsavedChanges: unsavedPosts.length > 0,
      unsavedPosts,
      message:
        unsavedPosts.length > 0
          ? `${unsavedPosts.length} post(s) have unsaved changes`
          : "All changes are saved",
    };
  } catch (error) {
    console.error("Failed to check for unsaved changes:", error);
    return {
      hasUnsavedChanges: false,
      unsavedPosts: [],
      message: "Error checking for unsaved changes",
    };
  }
};
