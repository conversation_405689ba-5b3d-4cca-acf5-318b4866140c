import React from 'react';
import { useApproval } from './ApprovalProvider.jsx';
import { filterPosts } from '../lib/postUtils.js';
import { Progress } from './ui/progress.jsx';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from './ui/select.jsx';
import { Badge } from './ui/badge.jsx';

/**
 * @typedef {import('../types/index.js').FilterType} FilterType
 * @typedef {import('../types/index.js').SummaryData} SummaryData
 */

/**
 * Filter options configuration
 */
const FILTER_OPTIONS = [
  { value: 'all', label: 'Todos os Posts', description: 'Mostrar todos os posts' },
  { value: 'approved', label: 'Aprovados', description: 'Posts aprovados' },
  { value: 'declined', label: 'Rejeitados', description: 'Posts rejeitados' },
  { value: 'favorited', label: 'Favoritos', description: 'Posts marcados como favoritos' },
  { value: 'with-notes', label: 'Com Notas', description: 'Posts que têm notas adicionadas' },
  { value: 'edited', label: 'Editados', description: 'Posts que foram editados' },
  { value: 'unreviewed', label: 'Não Revisados', description: 'Posts ainda não revisados' }
];

/**
 * ApprovalSummary component - displays summary statistics and filtering controls
 * @returns {JSX.Element}
 */
export function ApprovalSummary() {
  const { posts, summary, ui, setFilter } = useApproval();

  // Calculate completion progress based on reviewed posts (approved + declined)
  const completionProgress = summary.totalPosts > 0
    ? Math.round((summary.reviewedCount / summary.totalPosts) * 100)
    : 0;

  // Get current filter option
  const currentFilterOption = FILTER_OPTIONS.find(option => option.value === ui.filter) || FILTER_OPTIONS[0];

  // Get filtered posts count for display
  const filteredPosts = filterPosts(posts, ui.filter);
  const filteredCount = filteredPosts.length;

  /**
   * Handle filter change
   * @param {FilterType} newFilter 
   */
  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900 mb-1">
            Resumo da Aprovação
          </h2>
          <p className="text-sm text-gray-600">
            Acompanhe o progresso da revisão dos posts
          </p>
        </div>
        
        {/* Filter Controls */}
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-600">Filtrar:</span>
          <Select value={ui.filter} onValueChange={handleFilterChange}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Selecionar filtro" />
            </SelectTrigger>
            <SelectContent>
              {FILTER_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{option.label}</span>
                    <span className="text-xs text-gray-500">{option.description}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
        {/* Total Posts */}
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-900 mb-1">
            {summary.totalPosts}
          </div>
          <div className="text-sm text-gray-600">Total de Posts</div>
        </div>

        {/* Approved Posts */}
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600 mb-1">
            {summary.approvedCount}
          </div>
          <div className="text-sm text-gray-600">Aprovados</div>
        </div>

        {/* Declined Posts */}
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600 mb-1">
            {summary.declinedCount}
          </div>
          <div className="text-sm text-gray-600">Rejeitados</div>
        </div>

        {/* Favorited Posts */}
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600 mb-1">
            {summary.favoritedCount}
          </div>
          <div className="text-sm text-gray-600">Favoritos</div>
        </div>

        {/* Posts with Notes */}
        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-2xl font-bold text-orange-600 mb-1">
            {summary.notesCount}
          </div>
          <div className="text-sm text-gray-600">Com Notas</div>
        </div>

        {/* Edited Posts */}
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {summary.editedCount}
          </div>
          <div className="text-sm text-gray-600">Editados</div>
        </div>
      </div>

      {/* Progress Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">
            Progresso da Revisão
          </span>
          <span className="text-sm text-gray-600">
            {summary.reviewedCount} de {summary.totalPosts} revisados ({completionProgress}%)
          </span>
        </div>
        <Progress 
          value={completionProgress} 
          className="h-2 bg-gray-200"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>0%</span>
          <span>100%</span>
        </div>

        {/* Completion Message */}
        {completionProgress >= 100 && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="text-sm font-medium text-green-800">Revisão Completa!</div>
            <div className="text-xs text-green-600 mt-1">Todos os posts foram revisados</div>
          </div>
        )}
      </div>

      {/* Current Filter Status */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Visualizando:</span>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {currentFilterOption.label}
          </Badge>
        </div>
        
        <div className="text-sm text-gray-600">
          {filteredCount} de {summary.totalPosts} posts
        </div>
      </div>

      {/* Completion Status */}
      {completionProgress === 100 && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium text-green-800">
              Revisão Completa!
            </span>
          </div>
          <p className="text-sm text-green-700 mt-1">
            Todos os posts foram revisados. Você pode exportar os posts aprovados quando estiver pronto.
          </p>
        </div>
      )}

      {/* No Posts Message */}
      {summary.totalPosts === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">
            Nenhum post disponível para revisão
          </p>
        </div>
      )}
    </div>
  );
}

export default ApprovalSummary;