import React, { useState } from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from './ui/dialog';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';

/**
 * NotesDialog component for editing post notes
 * @param {Object} props
 * @param {boolean} props.open
 * @param {Function} props.onOpenChange
 * @param {string} props.notes
 * @param {Function} props.onSave
 * @param {string} props.postTitle
 */
function NotesDialog({ 
  open, 
  onOpenChange, 
  notes = '', 
  onSave, 
  postTitle = 'Post' 
}) {
  const [currentNotes, setCurrentNotes] = useState(notes);

  // Update local state when notes prop changes
  React.useEffect(() => {
    setCurrentNotes(notes);
  }, [notes]);

  const handleSave = () => {
    if (onSave) {
      onSave(currentNotes);
    }
    onOpenChange(false);
  };

  const handleCancel = () => {
    setCurrentNotes(notes); // Reset to original notes
    onOpenChange(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      handleCancel();
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSave();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Note</DialogTitle>
          <DialogDescription>
            Add your feedback or comments for "{postTitle}"
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Textarea
            placeholder="Enter your notes here..."
            value={currentNotes}
            onChange={(e) => setCurrentNotes(e.target.value)}
            onKeyDown={handleKeyDown}
            className="min-h-[120px] resize-none"
            autoFocus
          />
          <div className="text-xs text-gray-500">
            Press Ctrl+Enter (Cmd+Enter on Mac) to save, or Escape to cancel
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
          >
            Save Note
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default NotesDialog;