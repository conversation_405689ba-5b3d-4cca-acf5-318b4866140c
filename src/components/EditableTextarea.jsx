import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

/**
 * EditableTextarea component for inline editing functionality
 * @param {Object} props
 * @param {string} props.value - Current value of the textarea
 * @param {function} props.onSave - Callback when value is saved
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the textarea is disabled
 * @param {number} props.maxLength - Maximum character length
 * @param {number} props.minRows - Minimum number of rows
 * @param {number} props.maxRows - Maximum number of rows for auto-resize
 */
export function EditableTextarea({ 
  value = '', 
  onSave, 
  placeholder = 'Click to edit...', 
  className = '',
  disabled = false,
  maxLength = 500,
  minRows = 3,
  maxRows = 10
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaved, setIsSaved] = useState(false);
  const textareaRef = useRef(null);

  // Update editValue when value prop changes
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  // Focus textarea when entering edit mode
  useEffect(() => {
    if (isEditing && textareaRef.current) {
      // Use setTimeout to ensure the textarea is rendered
      setTimeout(() => {
        if (textareaRef.current) {
          textareaRef.current.focus();
          // Position cursor at end
          const length = textareaRef.current.value.length;
          textareaRef.current.setSelectionRange(length, length);
          autoResize();
        }
      }, 0);
    }
  }, [isEditing]);

  // Auto-resize functionality
  const autoResize = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      
      // Calculate the number of rows based on content
      const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight);
      const padding = parseInt(window.getComputedStyle(textarea).paddingTop) + 
                     parseInt(window.getComputedStyle(textarea).paddingBottom);
      
      const contentHeight = textarea.scrollHeight - padding;
      const rows = Math.ceil(contentHeight / lineHeight);
      
      // Constrain between minRows and maxRows
      const constrainedRows = Math.max(minRows, Math.min(maxRows, rows));
      const newHeight = (constrainedRows * lineHeight) + padding;
      
      textarea.style.height = `${newHeight}px`;
    }
  };

  const handleClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  const handleSave = () => {
    if (editValue !== value) {
      onSave?.(editValue);
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 1000); // Show saved state for 1 second
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(value); // Reset to original value
    setIsEditing(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      // Ctrl+Enter or Cmd+Enter to save
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    if (!maxLength || newValue.length <= maxLength) {
      setEditValue(newValue);
      autoResize();
    } else {
      // Prevent the change by keeping the current value
      e.target.value = editValue;
    }
  };

  // Calculate display rows for non-editing mode
  const getDisplayRows = () => {
    if (!value) return 1;
    const lines = value.split('\n').length;
    return Math.max(1, Math.min(lines, 3)); // Show max 3 lines in display mode
  };

  if (isEditing) {
    return (
      <div className={cn("relative", className)}>
        <textarea
          ref={textareaRef}
          value={editValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          maxLength={maxLength}
          className={cn(
            "border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            "border-blue-500 ring-blue-500/20 ring-2",
            "resize-none overflow-hidden"
          )}
          style={{ minHeight: `${minRows * 1.5}rem` }}
        />
        <div className="flex justify-between items-center mt-2">
          {maxLength && (
            <div className="text-xs text-gray-500">
              {editValue.length}/{maxLength}
            </div>
          )}
          <div className="text-xs text-gray-400">
            Ctrl+Enter to save, Esc to cancel
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={handleClick}
      className={cn(
        "min-h-[4rem] px-3 py-2 rounded-md border border-transparent cursor-pointer transition-all duration-200",
        "hover:border-gray-200 hover:bg-gray-50",
        "focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500",
        disabled && "cursor-not-allowed opacity-50",
        isSaved && "border-green-500 bg-green-50",
        value !== editValue && "border-blue-200 bg-blue-50", // Show edited state
        className
      )}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-label={`Click to edit ${placeholder}`}
    >
      <div 
        className={cn(
          "text-sm whitespace-pre-wrap break-words",
          !value && "text-gray-400 italic"
        )}
        style={{ 
          minHeight: `${getDisplayRows() * 1.5}rem`,
          maxHeight: '4.5rem', // Max 3 lines in display mode
          overflow: 'hidden'
        }}
      >
        {value || placeholder}
      </div>
      {value && value.length > 150 && (
        <div className="text-xs text-gray-400 mt-1">
          Click to expand...
        </div>
      )}
      {isSaved && (
        <div className="absolute -top-2 -right-2 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
      )}
    </div>
  );
}