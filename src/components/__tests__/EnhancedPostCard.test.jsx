import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import EnhancedPostCard, { PostCardSkeleton } from '../EnhancedPostCard';
import { ApprovalProvider } from '../ApprovalProvider';
import { TooltipProvider } from '../ui/tooltip';

// Mock the image loading
const mockPost = {
  id: '1',
  title: 'Test Post Title',
  caption: 'Test post caption content',
  hashtags: '#test #post #hashtags',
  image: '/test-image.jpg',
  category: 'test',
  isFavorited: false,
  notes: '',
  isEdited: false,
  editedContent: {},
  lastModified: new Date()
};

const mockEditedPost = {
  ...mockPost,
  id: '2',
  isEdited: true,
  editedContent: {
    title: 'Edited Title'
  }
};

const mockFavoritedPost = {
  ...mockPost,
  id: '3',
  isFavorited: true
};

const mockPostWithNotes = {
  ...mockPost,
  id: '4',
  notes: 'This is a test note'
};

const mockComplexPost = {
  ...mockPost,
  id: '5',
  isFavorited: true,
  isEdited: true,
  notes: 'Complex post with all states',
  editedContent: {
    title: 'Complex Edited Title',
    caption: 'Complex edited caption'
  }
};

// Test wrapper component
const TestWrapper = ({ children, initialPosts = [] }) => (
  <TooltipProvider>
    <ApprovalProvider initialPosts={initialPosts}>
      {children}
    </ApprovalProvider>
  </TooltipProvider>
);

describe('EnhancedPostCard', () => {

  describe('Basic Rendering', () => {
    it('renders post content correctly', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      expect(screen.getByText('Test Post Title')).toBeInTheDocument();
      expect(screen.getByText('Test post caption content')).toBeInTheDocument();
      expect(screen.getByText('#test #post #hashtags')).toBeInTheDocument();
      expect(screen.getByAltText('Test Post Title')).toBeInTheDocument();
    });

    it('renders with Google-inspired minimalist styling', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).toHaveClass('bg-white', 'border', 'border-gray-200/60', 'shadow-sm');
    });
  });

  describe('Visual State Indicators', () => {
    it('shows edit indicator when post is edited', () => {
      render(
        <TestWrapper initialPosts={[mockEditedPost]}>
          <EnhancedPostCard post={mockEditedPost} />
        </TestWrapper>
      );

      const editIndicator = screen.getByTitle('This post has been edited');
      expect(editIndicator).toBeInTheDocument();
      expect(editIndicator).toHaveClass('bg-blue-500', 'rounded-full', 'animate-pulse');
    });

    it('shows notes indicator when post has notes', () => {
      render(
        <TestWrapper initialPosts={[mockPostWithNotes]}>
          <EnhancedPostCard post={mockPostWithNotes} />
        </TestWrapper>
      );

      const notesIndicator = screen.getByTitle('This post has notes');
      expect(notesIndicator).toBeInTheDocument();
      expect(notesIndicator).toHaveClass('bg-orange-500', 'rounded-full');
    });

    it('shows favorite indicator when post is favorited', () => {
      render(
        <TestWrapper initialPosts={[mockFavoritedPost]}>
          <EnhancedPostCard post={mockFavoritedPost} />
        </TestWrapper>
      );

      const favoriteIndicator = screen.getByTitle('This post is favorited');
      expect(favoriteIndicator).toBeInTheDocument();
      expect(favoriteIndicator).toHaveClass('bg-green-500', 'rounded-full');
    });

    it('shows multiple indicators for complex post states', () => {
      render(
        <TestWrapper initialPosts={[mockComplexPost]}>
          <EnhancedPostCard post={mockComplexPost} />
        </TestWrapper>
      );

      expect(screen.getByTitle('This post has been edited')).toBeInTheDocument();
      expect(screen.getByTitle('This post has notes')).toBeInTheDocument();
      expect(screen.getByTitle('This post is favorited')).toBeInTheDocument();
    });
  });

  describe('Favorite State Styling', () => {
    it('does not apply border styling when favorited', () => {
      render(
        <TestWrapper initialPosts={[mockFavoritedPost]}>
          <EnhancedPostCard post={mockFavoritedPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      // Favorited posts should not have special border styling anymore
      expect(card).not.toHaveClass('ring-1', 'ring-green-400/60', 'bg-green-50/20');
    });

    it('does not show favorite background overlay', () => {
      render(
        <TestWrapper initialPosts={[mockFavoritedPost]}>
          <EnhancedPostCard post={mockFavoritedPost} />
        </TestWrapper>
      );

      // Look for the overlay div with green background - should not exist
      const imageContainer = screen.getByRole('img').parentElement.parentElement; // Go up to the relative div
      const overlay = imageContainer.querySelector('.bg-green-500\\/5');
      expect(overlay).not.toBeInTheDocument();
    });

    it('maintains consistent styling for non-favorited posts', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).not.toHaveClass('ring-green-400/60', 'bg-green-50/20');
    });
  });

  describe('Approval State Styling', () => {
    it('applies green styling for approved posts', () => {
      const approvedPost = { ...mockPost, approvalStatus: 'approved' };
      render(
        <TestWrapper initialPosts={[approvedPost]}>
          <EnhancedPostCard post={approvedPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).toHaveClass('ring-1', 'ring-green-400/60', 'bg-green-50/10');
    });

    it('applies red styling for declined posts', () => {
      const declinedPost = { ...mockPost, approvalStatus: 'declined' };
      render(
        <TestWrapper initialPosts={[declinedPost]}>
          <EnhancedPostCard post={declinedPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).toHaveClass('ring-1', 'ring-red-400/60', 'bg-red-50/10');
    });

    it('shows green overlay for approved posts', () => {
      const approvedPost = { ...mockPost, approvalStatus: 'approved' };
      render(
        <TestWrapper initialPosts={[approvedPost]}>
          <EnhancedPostCard post={approvedPost} />
        </TestWrapper>
      );

      const imageContainer = screen.getByRole('img').parentElement.parentElement;
      const overlay = imageContainer.querySelector('.bg-green-500\\/5');
      expect(overlay).toBeInTheDocument();
    });

    it('shows red overlay for declined posts', () => {
      const declinedPost = { ...mockPost, approvalStatus: 'declined' };
      render(
        <TestWrapper initialPosts={[declinedPost]}>
          <EnhancedPostCard post={declinedPost} />
        </TestWrapper>
      );

      const imageContainer = screen.getByRole('img').parentElement.parentElement;
      const overlay = imageContainer.querySelector('.bg-red-500\\/5');
      expect(overlay).toBeInTheDocument();
    });

    it('does not apply approval styling for pending posts', () => {
      const pendingPost = { ...mockPost, approvalStatus: 'pending' };
      render(
        <TestWrapper initialPosts={[pendingPost]}>
          <EnhancedPostCard post={pendingPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).not.toHaveClass('ring-green-400/60', 'bg-green-50/10');
      expect(card).not.toHaveClass('ring-red-400/60', 'bg-red-50/10');
    });
  });

  describe('Hover Effects', () => {
    it('applies hover effects with subtle lift and scale', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const card = screen.getByRole('img').closest('.post-card');
      expect(card).toHaveClass('hover:shadow-md', 'hover:scale-[1.02]', 'hover:border-gray-300/60');
    });
  });

  describe('Image Loading States', () => {
    it('shows image with loading container', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      // Should have image container with loading background
      const imageContainer = screen.getByRole('img').parentElement;
      expect(imageContainer).toHaveClass('bg-gray-100');
    });

    it('applies opacity transition classes to image', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const image = screen.getByAltText('Test Post Title');
      expect(image).toHaveClass('transition-opacity', 'duration-300');
    });
  });

  describe('Editable Content Integration', () => {
    it('renders editable input for title', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const titleElement = screen.getByText('Test Post Title');
      expect(titleElement.closest('[role="button"]')).toBeInTheDocument();
    });

    it('renders editable textarea for caption', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const captionElement = screen.getByText('Test post caption content');
      expect(captionElement.closest('[role="button"]')).toBeInTheDocument();
    });

    it('renders editable input for hashtags', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      const hashtagsElement = screen.getByText('#test #post #hashtags');
      expect(hashtagsElement.closest('[role="button"]')).toBeInTheDocument();
    });
  });

  describe('Post Actions Integration', () => {
    it('renders post actions component', () => {
      render(
        <TestWrapper initialPosts={[mockPost]}>
          <EnhancedPostCard post={mockPost} />
        </TestWrapper>
      );

      // Should have favorite and notes buttons
      const buttons = screen.getAllByRole('button').filter(btn => 
        btn.getAttribute('aria-label')?.includes('favorite') || 
        btn.getAttribute('aria-label')?.includes('note')
      );
      expect(buttons.length).toBeGreaterThanOrEqual(2);
    });
  });
});

describe('PostCardSkeleton', () => {
  it('renders skeleton loading state', () => {
    const { container } = render(<PostCardSkeleton />);

    // Should have animated skeleton elements
    const skeletonElements = container.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('has correct structure matching the main card', () => {
    const { container } = render(<PostCardSkeleton />);

    const card = container.querySelector('.post-card');
    expect(card).toHaveClass('post-card', 'overflow-hidden', 'bg-white');
    
    // Should have image skeleton area
    const imageSkeleton = card.querySelector('.h-\\[300px\\]');
    expect(imageSkeleton).toBeInTheDocument();
  });

  it('shows skeleton for all content areas', () => {
    const { container } = render(<PostCardSkeleton />);

    const card = container.querySelector('.post-card');
    
    // Should have multiple skeleton bars for different content
    const skeletonBars = card.querySelectorAll('.bg-gray-200.rounded.animate-pulse');
    expect(skeletonBars.length).toBeGreaterThanOrEqual(4); // Title, caption lines, hashtags
  });
});

describe('EnhancedPostCard Loading State', () => {
  it('shows skeleton when isLoading is true', () => {
    const { container } = render(
      <TestWrapper initialPosts={[mockPost]}>
        <EnhancedPostCard post={mockPost} isLoading={true} />
      </TestWrapper>
    );

    // Should show skeleton instead of actual content
    expect(screen.queryByText('Test Post Title')).not.toBeInTheDocument();
    
    // Should have skeleton elements
    const skeletonElements = container.querySelectorAll('.animate-pulse');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('shows actual content when isLoading is false', () => {
    render(
      <TestWrapper initialPosts={[mockPost]}>
        <EnhancedPostCard post={mockPost} isLoading={false} />
      </TestWrapper>
    );

    // Should show actual content
    expect(screen.getByText('Test Post Title')).toBeInTheDocument();
    expect(screen.getByText('Test post caption content')).toBeInTheDocument();
  });
});