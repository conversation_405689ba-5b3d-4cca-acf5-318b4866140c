import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { EditableTextarea } from '../EditableTextarea';

describe('EditableTextarea', () => {
  it('renders with initial value', () => {
    render(<EditableTextarea value="Test content" />);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('shows placeholder when no value provided', () => {
    render(<EditableTextarea placeholder="Enter description..." />);
    expect(screen.getByText('Enter description...')).toBeInTheDocument();
  });

  it('enters edit mode when clicked', () => {
    render(<EditableTextarea value="Test content" />);
    
    fireEvent.click(screen.getByText('Test content'));
    
    expect(screen.getByDisplayValue('Test content')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('saves value on Ctrl+Enter key press', () => {
    const onSave = vi.fn();
    render(<EditableTextarea value="Original content" onSave={onSave} />);
    
    fireEvent.click(screen.getByText('Original content'));
    const textarea = screen.getByDisplayValue('Original content');
    fireEvent.change(textarea, { target: { value: 'Modified content' } });
    fireEvent.keyDown(textarea, { key: 'Enter', ctrlKey: true });
    
    expect(onSave).toHaveBeenCalledWith('Modified content');
  });

  it('saves value on Cmd+Enter key press (Mac)', () => {
    const onSave = vi.fn();
    render(<EditableTextarea value="Original content" onSave={onSave} />);
    
    fireEvent.click(screen.getByText('Original content'));
    const textarea = screen.getByDisplayValue('Original content');
    fireEvent.change(textarea, { target: { value: 'Modified content' } });
    fireEvent.keyDown(textarea, { key: 'Enter', metaKey: true });
    
    expect(onSave).toHaveBeenCalledWith('Modified content');
  });

  it('cancels edit on Escape key press', () => {
    const onSave = vi.fn();
    render(<EditableTextarea value="Original content" onSave={onSave} />);
    
    fireEvent.click(screen.getByText('Original content'));
    const textarea = screen.getByDisplayValue('Original content');
    fireEvent.change(textarea, { target: { value: 'Modified content' } });
    fireEvent.keyDown(textarea, { key: 'Escape' });
    
    expect(onSave).not.toHaveBeenCalled();
    expect(screen.getByText('Original content')).toBeInTheDocument();
  });

  it('saves value on blur', () => {
    const onSave = vi.fn();
    render(<EditableTextarea value="Original content" onSave={onSave} />);
    
    fireEvent.click(screen.getByText('Original content'));
    const textarea = screen.getByDisplayValue('Original content');
    fireEvent.change(textarea, { target: { value: 'Modified content' } });
    fireEvent.blur(textarea);
    
    expect(onSave).toHaveBeenCalledWith('Modified content');
  });

  it('respects maxLength constraint', () => {
    render(<EditableTextarea value="Test" maxLength={10} />);
    
    fireEvent.click(screen.getByText('Test'));
    const textarea = screen.getByDisplayValue('Test');
    fireEvent.change(textarea, { target: { value: 'This is way too long for the limit' } });
    
    expect(textarea.value).toBe('Test');
  });

  it('shows character count when maxLength is set', () => {
    render(<EditableTextarea value="Test content" maxLength={100} />);
    
    fireEvent.click(screen.getByText('Test content'));
    
    expect(screen.getByText('12/100')).toBeInTheDocument();
  });

  it('shows keyboard shortcuts hint in edit mode', () => {
    render(<EditableTextarea value="Test content" />);
    
    fireEvent.click(screen.getByText('Test content'));
    
    expect(screen.getByText('Ctrl+Enter to save, Esc to cancel')).toBeInTheDocument();
  });

  it('does not enter edit mode when disabled', () => {
    render(<EditableTextarea value="Test content" disabled />);
    
    fireEvent.click(screen.getByText('Test content'));
    
    expect(screen.queryByRole('textbox')).not.toBeInTheDocument();
  });

  it('handles multiline content properly', () => {
    const multilineContent = "Line 1\nLine 2\nLine 3";
    render(<EditableTextarea value={multilineContent} />);
    
    // Check that the content is rendered
    expect(screen.getByRole('button')).toBeInTheDocument();
    
    // Click to enter edit mode
    fireEvent.click(screen.getByRole('button'));
    const textarea = screen.getByRole('textbox');
    expect(textarea).toBeInTheDocument();
    expect(textarea.value).toBe(multilineContent);
  });

  it('shows expand hint for long content', () => {
    const longContent = "This is a very long piece of content that exceeds the typical display length and should trigger the expand hint to appear when displayed in non-editing mode.";
    render(<EditableTextarea value={longContent} />);
    
    expect(screen.getByText('Click to expand...')).toBeInTheDocument();
  });

  it('handles keyboard navigation with tab', () => {
    render(<EditableTextarea value="Test content" />);
    
    const element = screen.getByText('Test content').parentElement;
    expect(element).toHaveAttribute('tabIndex', '0');
  });

  it('respects minRows prop', () => {
    render(<EditableTextarea value="Test" minRows={5} />);
    
    fireEvent.click(screen.getByText('Test'));
    const textarea = screen.getByDisplayValue('Test');
    expect(textarea).toHaveStyle({ minHeight: '7.5rem' });
  });

  it('handles auto-resize functionality', () => {
    render(<EditableTextarea value="Test" />);
    
    fireEvent.click(screen.getByText('Test'));
    const textarea = screen.getByDisplayValue('Test');
    
    fireEvent.change(textarea, { 
      target: { value: 'Line 1\nLine 2\nLine 3\nLine 4\nLine 5' } 
    });
    
    expect(textarea.value).toBe('Line 1\nLine 2\nLine 3\nLine 4\nLine 5');
  });

  it('preserves whitespace and line breaks in display mode', () => {
    const contentWithWhitespace = "  Indented text\n\nWith empty lines  ";
    render(<EditableTextarea value={contentWithWhitespace} />);
    
    // Find the inner div that contains the content
    const displayElement = screen.getByRole('button').querySelector('div');
    expect(displayElement).toHaveClass('whitespace-pre-wrap');
    expect(displayElement.textContent).toBe(contentWithWhitespace);
  });

  it('shows edited state when value differs from original', () => {
    render(<EditableTextarea value="Original" />);
    
    const container = screen.getByText('Original').parentElement;
    expect(container).toHaveClass('border-transparent');
  });
});