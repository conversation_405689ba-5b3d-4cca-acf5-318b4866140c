import { describe, it, expect, beforeEach } from "vitest";
import { ACTIONS } from "../ApprovalProvider.jsx";

// Mock the posts data for testing
const mockPosts = [
  {
    id: 1,
    title: "Test Post 1",
    image: "test1.png",
    caption: "Test caption 1",
    hashtags: "#test1",
    category: "original",
  },
  {
    id: 2,
    title: "Test Post 2",
    image: "test2.png",
    caption: "Test caption 2",
    hashtags: "#test2",
    category: "original",
  },
];

// Helper function to transform original posts to enhanced posts (copied from ApprovalProvider)
const transformToEnhancedPost = (originalPost) => ({
  id: originalPost.id.toString(),
  image: originalPost.image,
  category: originalPost.category,
  originalContent: {
    title: originalPost.title,
    caption: originalPost.caption,
    hashtags: originalPost.hashtags,
  },
  isFavorited: false,
  notes: "",
  isEdited: false,
  editedContent: {},
  lastModified: new Date(),
});

// Helper function to compute summary data (copied from ApprovalProvider)
const computeSummary = (posts) => {
  const postsArray = Object.values(posts);
  return {
    totalPosts: postsArray.length,
    favoritedCount: postsArray.filter((post) => post.isFavorited).length,
    notesCount: postsArray.filter((post) => post.notes.trim().length > 0)
      .length,
    editedCount: postsArray.filter((post) => post.isEdited).length,
  };
};

// Reducer function (copied from ApprovalProvider for testing)
const approvalReducer = (state, action) => {
  switch (action.type) {
    case ACTIONS.UPDATE_POST: {
      const { postId, updates } = action.payload;
      const post = state.posts[postId];

      if (!post) return state;

      const updatedPost = {
        ...post,
        editedContent: {
          ...post.editedContent,
          ...updates,
        },
        isEdited: true,
        lastModified: new Date(),
      };

      const newPosts = {
        ...state.posts,
        [postId]: updatedPost,
      };

      return {
        ...state,
        posts: newPosts,
        summary: computeSummary(newPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date(),
        },
      };
    }

    case ACTIONS.TOGGLE_FAVORITE: {
      const { postId } = action.payload;
      const post = state.posts[postId];

      if (!post) return state;

      const updatedPost = {
        ...post,
        isFavorited: !post.isFavorited,
        lastModified: new Date(),
      };

      const newPosts = {
        ...state.posts,
        [postId]: updatedPost,
      };

      return {
        ...state,
        posts: newPosts,
        summary: computeSummary(newPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date(),
        },
      };
    }

    case ACTIONS.UPDATE_NOTES: {
      const { postId, notes } = action.payload;
      const post = state.posts[postId];

      if (!post) return state;

      const updatedPost = {
        ...post,
        notes: notes || "",
        lastModified: new Date(),
      };

      const newPosts = {
        ...state.posts,
        [postId]: updatedPost,
      };

      return {
        ...state,
        posts: newPosts,
        summary: computeSummary(newPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date(),
        },
      };
    }

    case ACTIONS.SET_FILTER: {
      return {
        ...state,
        ui: {
          ...state.ui,
          filter: action.payload.filter,
        },
      };
    }

    case ACTIONS.SET_EDITING: {
      return {
        ...state,
        ui: {
          ...state.ui,
          editingPostId: action.payload.postId,
        },
      };
    }

    case ACTIONS.SET_VIEW_MODE: {
      return {
        ...state,
        ui: {
          ...state.ui,
          viewMode: action.payload.viewMode,
        },
      };
    }

    case ACTIONS.SET_ADMIN_AUTHENTICATED: {
      return {
        ...state,
        ui: {
          ...state.ui,
          isAdminAuthenticated: action.payload.isAuthenticated,
        },
      };
    }

    case ACTIONS.SET_SHOW_PASSCODE_DIALOG: {
      return {
        ...state,
        ui: {
          ...state.ui,
          showPasscodeDialog: action.payload.show,
        },
      };
    }

    case ACTIONS.LOAD_STATE: {
      const { loadedState } = action.payload;
      return {
        ...state,
        ...loadedState,
        summary: computeSummary(loadedState.posts || state.posts),
      };
    }

    default:
      return state;
  }
};
describe("ApprovalProvider Reducer", () => {
  let initialState;

  beforeEach(() => {
    // Create initial state for each test
    initialState = {
      posts: mockPosts.reduce((acc, post) => {
        const enhancedPost = transformToEnhancedPost(post);
        acc[enhancedPost.id] = enhancedPost;
        return acc;
      }, {}),
      ui: {
        editingPostId: null,
        filter: "all",
        isLoading: false,
        lastSaved: null,
        viewMode: "client",
        isAdminAuthenticated: false,
        showPasscodeDialog: false,
      },
      summary: computeSummary(
        mockPosts.reduce((acc, post) => {
          const enhancedPost = transformToEnhancedPost(post);
          acc[enhancedPost.id] = enhancedPost;
          return acc;
        }, {})
      ),
    };
  });

  describe("UPDATE_POST action", () => {
    it("should update post content and mark as edited", () => {
      const action = {
        type: ACTIONS.UPDATE_POST,
        payload: {
          postId: "1",
          updates: {
            title: "Updated Title",
            caption: "Updated Caption",
          },
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].editedContent.title).toBe("Updated Title");
      expect(newState.posts["1"].editedContent.caption).toBe("Updated Caption");
      expect(newState.posts["1"].isEdited).toBe(true);
      expect(newState.posts["1"].lastModified).toBeInstanceOf(Date);
      expect(newState.summary.editedCount).toBe(1);
      expect(newState.ui.lastSaved).toBeInstanceOf(Date);
    });

    it("should preserve original content when updating", () => {
      const action = {
        type: ACTIONS.UPDATE_POST,
        payload: {
          postId: "1",
          updates: { title: "Updated Title" },
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].originalContent.title).toBe("Test Post 1");
      expect(newState.posts["1"].originalContent.caption).toBe(
        "Test caption 1"
      );
      expect(newState.posts["1"].editedContent.title).toBe("Updated Title");
      expect(newState.posts["1"].editedContent.caption).toBeUndefined();
    });

    it("should return unchanged state for non-existent post", () => {
      const action = {
        type: ACTIONS.UPDATE_POST,
        payload: {
          postId: "999",
          updates: { title: "Updated Title" },
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState).toBe(initialState);
    });
  });

  describe("TOGGLE_FAVORITE action", () => {
    it("should toggle favorite status from false to true", () => {
      const action = {
        type: ACTIONS.TOGGLE_FAVORITE,
        payload: { postId: "1" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].isFavorited).toBe(true);
      expect(newState.posts["1"].lastModified).toBeInstanceOf(Date);
      expect(newState.summary.favoritedCount).toBe(1);
      expect(newState.ui.lastSaved).toBeInstanceOf(Date);
    });

    it("should toggle favorite status from true to false", () => {
      // First set the post as favorited
      initialState.posts["1"].isFavorited = true;
      initialState.summary.favoritedCount = 1;

      const action = {
        type: ACTIONS.TOGGLE_FAVORITE,
        payload: { postId: "1" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].isFavorited).toBe(false);
      expect(newState.summary.favoritedCount).toBe(0);
    });

    it("should return unchanged state for non-existent post", () => {
      const action = {
        type: ACTIONS.TOGGLE_FAVORITE,
        payload: { postId: "999" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState).toBe(initialState);
    });
  });

  describe("UPDATE_NOTES action", () => {
    it("should update post notes", () => {
      const action = {
        type: ACTIONS.UPDATE_NOTES,
        payload: {
          postId: "1",
          notes: "This is a test note",
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].notes).toBe("This is a test note");
      expect(newState.posts["1"].lastModified).toBeInstanceOf(Date);
      expect(newState.summary.notesCount).toBe(1);
      expect(newState.ui.lastSaved).toBeInstanceOf(Date);
    });

    it("should handle empty notes", () => {
      const action = {
        type: ACTIONS.UPDATE_NOTES,
        payload: {
          postId: "1",
          notes: "",
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].notes).toBe("");
      expect(newState.summary.notesCount).toBe(0);
    });

    it("should handle null notes", () => {
      const action = {
        type: ACTIONS.UPDATE_NOTES,
        payload: {
          postId: "1",
          notes: null,
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["1"].notes).toBe("");
    });

    it("should return unchanged state for non-existent post", () => {
      const action = {
        type: ACTIONS.UPDATE_NOTES,
        payload: {
          postId: "999",
          notes: "Test note",
        },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState).toBe(initialState);
    });
  });

  describe("UI state actions", () => {
    it("should update filter", () => {
      const action = {
        type: ACTIONS.SET_FILTER,
        payload: { filter: "favorited" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.ui.filter).toBe("favorited");
    });

    it("should update editing post ID", () => {
      const action = {
        type: ACTIONS.SET_EDITING,
        payload: { postId: "1" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.ui.editingPostId).toBe("1");
    });

    it("should update view mode", () => {
      const action = {
        type: ACTIONS.SET_VIEW_MODE,
        payload: { viewMode: "admin" },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.ui.viewMode).toBe("admin");
    });

    it("should update admin authentication status", () => {
      const action = {
        type: ACTIONS.SET_ADMIN_AUTHENTICATED,
        payload: { isAuthenticated: true },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.ui.isAdminAuthenticated).toBe(true);
    });

    it("should update passcode dialog visibility", () => {
      const action = {
        type: ACTIONS.SET_SHOW_PASSCODE_DIALOG,
        payload: { show: true },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.ui.showPasscodeDialog).toBe(true);
    });
  });

  describe("LOAD_STATE action", () => {
    it("should load new state and recompute summary", () => {
      const loadedState = {
        posts: {
          3: {
            id: "3",
            isFavorited: true,
            notes: "Loaded note",
            isEdited: true,
          },
        },
        ui: {
          filter: "favorited",
          viewMode: "admin",
        },
      };

      const action = {
        type: ACTIONS.LOAD_STATE,
        payload: { loadedState },
      };

      const newState = approvalReducer(initialState, action);

      expect(newState.posts["3"]).toBeDefined();
      expect(newState.ui.filter).toBe("favorited");
      expect(newState.ui.viewMode).toBe("admin");
      expect(newState.summary.favoritedCount).toBe(1);
      expect(newState.summary.notesCount).toBe(1);
      expect(newState.summary.editedCount).toBe(1);
    });
  });

  describe("Unknown action", () => {
    it("should return unchanged state for unknown action", () => {
      const action = {
        type: "UNKNOWN_ACTION",
        payload: {},
      };

      const newState = approvalReducer(initialState, action);

      expect(newState).toBe(initialState);
    });
  });

  describe("Summary computation", () => {
    it("should correctly compute summary statistics", () => {
      // Set up test data
      initialState.posts["1"].isFavorited = true;
      initialState.posts["1"].notes = "Test note";
      initialState.posts["1"].isEdited = true;
      initialState.posts["2"].isFavorited = true;

      const summary = computeSummary(initialState.posts);

      expect(summary.totalPosts).toBe(2);
      expect(summary.favoritedCount).toBe(2);
      expect(summary.notesCount).toBe(1);
      expect(summary.editedCount).toBe(1);
    });

    it("should handle empty posts object", () => {
      const summary = computeSummary({});

      expect(summary.totalPosts).toBe(0);
      expect(summary.favoritedCount).toBe(0);
      expect(summary.notesCount).toBe(0);
      expect(summary.editedCount).toBe(0);
    });
  });
});
