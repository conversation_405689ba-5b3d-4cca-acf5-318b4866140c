import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Post from '../Post';
import PostActions from '../PostActions';
import { ApprovalProvider, useApproval } from '../ApprovalProvider';

// Mock the UI components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }) => <div className={className}>{children}</div>,
  CardContent: ({ children, className }) => <div className={className}>{children}</div>
}));

// Mock utils
vi.mock('../../lib/utils', () => ({
  cn: (...classes) => classes.filter(Boolean).join(' ')
}));

// Mock storage
vi.mock('../../lib/storage', () => ({
  loadApprovalState: () => null,
  debouncedSaveApprovalState: vi.fn(),
  isStorageAvailable: () => true,
  checkForUnsavedChanges: () => ({ hasUnsavedChanges: false })
}));

describe('Favorite Functionality', () => {
  const mockPost = {
    id: '1',
    title: 'Test Post',
    caption: 'Test caption',
    hashtags: '#test',
    image: '/test-image.jpg',
    isFavorited: false,
    notes: '',
    isEdited: false,
    editedContent: {},
    lastModified: new Date()
  };

  const renderWithProvider = (initialPosts = [mockPost]) => {
    const TestComponent = () => {
      const { posts } = useApproval();
      const postsArray = Object.values(posts);
      
      return (
        <div>
          {postsArray.map(post => (
            <Post key={post.id} post={post} />
          ))}
        </div>
      );
    };

    return render(
      <ApprovalProvider initialPosts={initialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );
  };

  describe('Heart Icon Toggle', () => {
    it('shows unfavorited state initially', () => {
      renderWithProvider();
      
      const heartButton = screen.getByLabelText('Add to favorites');
      expect(heartButton).toBeInTheDocument();
    });

    it('toggles to favorited state when clicked', async () => {
      renderWithProvider();
      
      const heartButton = screen.getByLabelText('Add to favorites');
      fireEvent.click(heartButton);

      await waitFor(() => {
        expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
      });
    });

    it('toggles back to unfavorited state when clicked again', async () => {
      renderWithProvider();

      // First click to favorite the post
      const heartButton = screen.getByLabelText('Add to favorites');
      fireEvent.click(heartButton);

      // Wait for the state to update to favorited
      await waitFor(() => {
        expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
      });

      // Second click to unfavorite the post
      const unfavoriteButton = screen.getByLabelText('Remove from favorites');
      fireEvent.click(unfavoriteButton);

      await waitFor(() => {
        expect(screen.getByLabelText('Add to favorites')).toBeInTheDocument();
      });
    });
  });

  describe('Visual State Changes', () => {
    it('applies gray color for unfavorited posts', () => {
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={vi.fn()}
          onToggleNotes={vi.fn()}
        />
      );

      const heartIcon = screen.getByLabelText('Add to favorites').querySelector('svg');
      expect(heartIcon).toHaveClass('text-gray-500');
    });

    it('applies red color for favorited posts', () => {
      const favoritedPost = { ...mockPost, isFavorited: true };
      
      render(
        <PostActions
          post={favoritedPost}
          onToggleFavorite={vi.fn()}
          onToggleNotes={vi.fn()}
        />
      );

      const heartIcon = screen.getByLabelText('Remove from favorites').querySelector('svg');
      expect(heartIcon).toHaveClass('text-red-500', 'fill-red-500');
    });

    it('does not apply border effects for favorited posts', () => {
      const favoritedPost = { ...mockPost, isFavorited: true };
      renderWithProvider([favoritedPost]);

      const postCard = screen.getByRole('img').closest('.post-card');
      // Favorited posts should not have special border styling anymore
      expect(postCard).not.toHaveClass('ring-1', 'ring-green-400', 'bg-green-50/20');
    });

    it('maintains consistent styling when post is unfavorited', async () => {
      renderWithProvider();

      // First click to favorite the post
      const heartButton = screen.getByLabelText('Add to favorites');
      fireEvent.click(heartButton);

      // Wait for the state to update to favorited
      await waitFor(() => {
        expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
      });

      // Second click to unfavorite the post
      const unfavoriteButton = screen.getByLabelText('Remove from favorites');
      fireEvent.click(unfavoriteButton);

      await waitFor(() => {
        const postCard = screen.getByRole('img').closest('.post-card');
        // Should maintain base styling without favorite-specific classes
        expect(postCard).not.toHaveClass('ring-1', 'ring-green-400', 'bg-green-50/20');
      });
    });
  });

  describe('Smooth Transitions', () => {
    it('applies transition classes to post card', () => {
      renderWithProvider();

      const postCard = screen.getByRole('img').closest('.post-card');
      expect(postCard).toHaveClass('transition-all', 'duration-200');
    });

    it('applies transition classes to heart icon', () => {
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={vi.fn()}
          onToggleNotes={vi.fn()}
        />
      );

      const heartIcon = screen.getByLabelText('Add to favorites').querySelector('svg');
      expect(heartIcon).toHaveClass('transition-all', 'duration-200');
    });

    it('applies hover effects to action buttons', () => {
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={vi.fn()}
          onToggleNotes={vi.fn()}
        />
      );

      const heartButton = screen.getByLabelText('Add to favorites');
      expect(heartButton).toHaveClass('hover:scale-105', 'active:scale-95');
    });
  });

  describe('Bounce Animation', () => {
    it('adds bounce animation class on favorite toggle', async () => {
      const mockToggleFavorite = vi.fn();
      
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={mockToggleFavorite}
          onToggleNotes={vi.fn()}
        />
      );

      const heartButton = screen.getByLabelText('Add to favorites');
      
      // Mock the classList methods
      const mockClassList = {
        add: vi.fn(),
        remove: vi.fn()
      };
      
      Object.defineProperty(heartButton, 'classList', {
        value: mockClassList,
        writable: true
      });

      fireEvent.click(heartButton);

      expect(mockToggleFavorite).toHaveBeenCalledWith('1');
    });
  });

  describe('State Management', () => {
    it('persists favorite state across re-renders', async () => {
      renderWithProvider();
      
      const heartButton = screen.getByLabelText('Add to favorites');
      fireEvent.click(heartButton);

      await waitFor(() => {
        expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
      });

      // State should persist within the same provider instance
      expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
    });

    it('handles multiple posts independently', async () => {
      const post1 = { ...mockPost, id: '1' };
      const post2 = { ...mockPost, id: '2', title: 'Post 2' };

      renderWithProvider([post1, post2]);

      const heartButtons = screen.getAllByLabelText('Add to favorites');
      expect(heartButtons).toHaveLength(2);

      // Favorite only the first post
      fireEvent.click(heartButtons[0]);

      await waitFor(() => {
        expect(screen.getByLabelText('Remove from favorites')).toBeInTheDocument();
        expect(screen.getByLabelText('Add to favorites')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles missing onToggleFavorite gracefully', () => {
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={null}
          onToggleNotes={vi.fn()}
        />
      );

      const heartButton = screen.getByLabelText('Add to favorites');
      
      expect(() => {
        fireEvent.click(heartButton);
      }).not.toThrow();
    });

    it('handles disabled state correctly', () => {
      const mockToggleFavorite = vi.fn();
      
      render(
        <PostActions
          post={mockPost}
          onToggleFavorite={mockToggleFavorite}
          onToggleNotes={vi.fn()}
          disabled={true}
        />
      );

      const heartButton = screen.getByLabelText('Add to favorites');
      fireEvent.click(heartButton);

      expect(mockToggleFavorite).not.toHaveBeenCalled();
    });
  });
});