import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import NotesDialog from '../NotesDialog';

// Mock the UI components
vi.mock('../ui/dialog', () => ({
  Dialog: ({ children, open, onOpenChange }) => 
    open ? <div data-testid="dialog" onClick={() => onOpenChange(false)}>{children}</div> : null,
  DialogContent: ({ children, className }) => <div className={className}>{children}</div>,
  DialogHeader: ({ children }) => <div>{children}</div>,
  DialogTitle: ({ children }) => <h2>{children}</h2>,
  DialogDescription: ({ children }) => <p>{children}</p>,
  DialogFooter: ({ children }) => <div>{children}</div>
}));

vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  )
}));

vi.mock('../ui/textarea', () => ({
  Textarea: ({ value, onChange, onKeyDown, placeholder, className, autoFocus, ...props }) => (
    <textarea
      value={value}
      onChange={onChange}
      onKeyDown={onKeyDown}
      placeholder={placeholder}
      className={className}
      autoFocus={autoFocus}
      {...props}
    />
  )
}));

describe('NotesDialog', () => {
  const defaultProps = {
    open: true,
    onOpenChange: vi.fn(),
    notes: '',
    onSave: vi.fn(),
    postTitle: 'Test Post'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders when open is true', () => {
    render(<NotesDialog {...defaultProps} />);
    
    expect(screen.getByTestId('dialog')).toBeInTheDocument();
    expect(screen.getByText('Add Note')).toBeInTheDocument();
    expect(screen.getByText('Add your feedback or comments for "Test Post"')).toBeInTheDocument();
  });

  it('does not render when open is false', () => {
    render(<NotesDialog {...defaultProps} open={false} />);
    
    expect(screen.queryByTestId('dialog')).not.toBeInTheDocument();
  });

  it('displays existing notes in textarea', () => {
    const existingNotes = 'This is a test note';
    render(<NotesDialog {...defaultProps} notes={existingNotes} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    expect(textarea.value).toBe(existingNotes);
  });

  it('updates textarea value when typing', () => {
    render(<NotesDialog {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'New note content' } });
    
    expect(textarea.value).toBe('New note content');
  });

  it('calls onSave with current notes when Save button is clicked', () => {
    const mockOnSave = vi.fn();
    render(<NotesDialog {...defaultProps} onSave={mockOnSave} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'Test note' } });
    
    const saveButton = screen.getByText('Save Note');
    fireEvent.click(saveButton);
    
    expect(mockOnSave).toHaveBeenCalledWith('Test note');
  });

  it('calls onOpenChange(false) when Save button is clicked', () => {
    const mockOnOpenChange = vi.fn();
    render(<NotesDialog {...defaultProps} onOpenChange={mockOnOpenChange} />);
    
    const saveButton = screen.getByText('Save Note');
    fireEvent.click(saveButton);
    
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('resets notes and closes dialog when Cancel button is clicked', () => {
    const mockOnOpenChange = vi.fn();
    const originalNotes = 'Original note';
    render(<NotesDialog {...defaultProps} notes={originalNotes} onOpenChange={mockOnOpenChange} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'Modified note' } });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    expect(textarea.value).toBe(originalNotes);
  });

  it('saves notes when Ctrl+Enter is pressed', () => {
    const mockOnSave = vi.fn();
    const mockOnOpenChange = vi.fn();
    render(<NotesDialog {...defaultProps} onSave={mockOnSave} onOpenChange={mockOnOpenChange} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'Keyboard save test' } });
    fireEvent.keyDown(textarea, { key: 'Enter', ctrlKey: true });
    
    expect(mockOnSave).toHaveBeenCalledWith('Keyboard save test');
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('saves notes when Cmd+Enter is pressed (Mac)', () => {
    const mockOnSave = vi.fn();
    const mockOnOpenChange = vi.fn();
    render(<NotesDialog {...defaultProps} onSave={mockOnSave} onOpenChange={mockOnOpenChange} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'Mac keyboard save test' } });
    fireEvent.keyDown(textarea, { key: 'Enter', metaKey: true });
    
    expect(mockOnSave).toHaveBeenCalledWith('Mac keyboard save test');
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
  });

  it('cancels and closes dialog when Escape is pressed', () => {
    const mockOnOpenChange = vi.fn();
    const originalNotes = 'Original note';
    render(<NotesDialog {...defaultProps} notes={originalNotes} onOpenChange={mockOnOpenChange} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    fireEvent.change(textarea, { target: { value: 'Modified note' } });
    fireEvent.keyDown(textarea, { key: 'Escape' });
    
    expect(mockOnOpenChange).toHaveBeenCalledWith(false);
    expect(textarea.value).toBe(originalNotes);
  });

  it('updates local state when notes prop changes', async () => {
    const { rerender } = render(<NotesDialog {...defaultProps} notes="Initial note" />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    expect(textarea.value).toBe('Initial note');
    
    rerender(<NotesDialog {...defaultProps} notes="Updated note" />);
    
    await waitFor(() => {
      expect(textarea.value).toBe('Updated note');
    });
  });

  it('shows keyboard shortcuts hint', () => {
    render(<NotesDialog {...defaultProps} />);
    
    expect(screen.getByText(/Press Ctrl\+Enter.*to save.*Escape to cancel/)).toBeInTheDocument();
  });

  it('passes autoFocus prop to textarea', () => {
    render(<NotesDialog {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Enter your notes here...');
    // Check that autoFocus prop is passed (the mock component should receive it)
    expect(textarea).toBeInTheDocument();
    // Since we're using a mock, we can't test the actual autoFocus behavior
    // but we can verify the component renders correctly
  });

  it('handles missing onSave gracefully', () => {
    render(<NotesDialog {...defaultProps} onSave={null} />);
    
    const saveButton = screen.getByText('Save Note');
    
    expect(() => {
      fireEvent.click(saveButton);
    }).not.toThrow();
  });

  it('displays correct dialog title and description', () => {
    render(<NotesDialog {...defaultProps} postTitle="My Custom Post" />);
    
    expect(screen.getByText('Add Note')).toBeInTheDocument();
    expect(screen.getByText('Add your feedback or comments for "My Custom Post"')).toBeInTheDocument();
  });
});