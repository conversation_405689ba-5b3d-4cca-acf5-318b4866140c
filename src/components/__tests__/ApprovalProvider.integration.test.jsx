import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { render, act } from "@testing-library/react";
import React from "react";
import { ApprovalProvider, useApproval } from "../ApprovalProvider.jsx";
import * as storage from "../../lib/storage.js";

// Mock the storage module
vi.mock("../../lib/storage.js", () => ({
  loadApprovalState: vi.fn(),
  debouncedSaveApprovalState: vi.fn(),
  isStorageAvailable: vi.fn(),
  checkForUnsavedChanges: vi.fn(),
}));

// Test component that uses the approval context
function TestComponent() {
  const approval = useApproval();

  return (
    <div>
      <div data-testid="posts-count">{Object.keys(approval.posts).length}</div>
      <div data-testid="summary-total">{approval.summary.totalPosts}</div>
      <div data-testid="filter">{approval.ui.filter}</div>
      <button
        data-testid="toggle-favorite"
        onClick={() => approval.toggleFavorite("1")}
      >
        Toggle Favorite
      </button>
      <button
        data-testid="update-notes"
        onClick={() => approval.updateNotes("1", "Test note")}
      >
        Update Notes
      </button>
      <button
        data-testid="check-unsaved"
        onClick={() => {
          const result = approval.checkUnsavedChanges();
          console.log("Unsaved changes:", result);
        }}
      >
        Check Unsaved
      </button>
    </div>
  );
}

const mockInitialPosts = [
  {
    id: "1",
    title: "Test Post 1",
    caption: "Test caption 1",
    hashtags: "#test1",
    image: "test1.png",
    category: "original",
  },
  {
    id: "2",
    title: "Test Post 2",
    caption: "Test caption 2",
    hashtags: "#test2",
    image: "test2.png",
    category: "original",
  },
];

describe("ApprovalProvider Integration", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock implementations
    storage.isStorageAvailable.mockReturnValue(true);
    storage.loadApprovalState.mockReturnValue(null);
    storage.debouncedSaveApprovalState.mockImplementation(() => {});
    storage.checkForUnsavedChanges.mockReturnValue({
      hasUnsavedChanges: false,
      unsavedPosts: [],
      message: "All changes are saved",
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("should initialize with initial posts when no saved state exists", () => {
    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    expect(getByTestId("posts-count")).toHaveTextContent("2");
    expect(getByTestId("summary-total")).toHaveTextContent("2");
    expect(getByTestId("filter")).toHaveTextContent("all");
  });

  it("should load saved state when available", () => {
    const savedState = {
      posts: {
        1: {
          id: "1",
          title: "Saved Post 1",
          isFavorited: true,
          notes: "Saved note",
          isEdited: false,
          editedContent: {},
          lastModified: new Date(),
        },
      },
      ui: {
        filter: "favorited",
        viewMode: "admin",
        isAdminAuthenticated: true,
      },
    };

    storage.loadApprovalState.mockReturnValue(savedState);

    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    expect(getByTestId("filter")).toHaveTextContent("favorited");
    expect(storage.loadApprovalState).toHaveBeenCalled();
  });

  it("should merge saved state with new initial posts", () => {
    const savedState = {
      posts: {
        1: {
          id: "1",
          title: "Saved Post 1",
          isFavorited: true,
          notes: "Saved note",
          isEdited: false,
          editedContent: {},
          lastModified: new Date(),
        },
        // Note: post '2' is not in saved state but is in initialPosts
      },
      ui: {
        filter: "all",
        viewMode: "client",
        isAdminAuthenticated: false,
      },
    };

    storage.loadApprovalState.mockReturnValue(savedState);

    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    // Should have both saved post and new post from initialPosts
    expect(getByTestId("posts-count")).toHaveTextContent("2");
  });

  it("should auto-save state changes", async () => {
    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    // Trigger a state change
    act(() => {
      getByTestId("toggle-favorite").click();
    });

    // Should call debounced save
    expect(storage.debouncedSaveApprovalState).toHaveBeenCalled();
  });

  it("should handle storage unavailable gracefully", () => {
    storage.isStorageAvailable.mockReturnValue(false);

    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {});

    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    expect(getByTestId("posts-count")).toHaveTextContent("2");
    expect(consoleSpy).toHaveBeenCalledWith(
      "localStorage is not available, persistence disabled"
    );

    consoleSpy.mockRestore();
  });

  it("should provide unsaved changes check functionality", () => {
    storage.checkForUnsavedChanges.mockReturnValue({
      hasUnsavedChanges: true,
      unsavedPosts: [{ id: "1", reason: "modified_after_save" }],
      message: "1 post(s) have unsaved changes",
    });

    const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

    const { getByTestId } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    act(() => {
      getByTestId("check-unsaved").click();
    });

    expect(storage.checkForUnsavedChanges).toHaveBeenCalled();
    expect(consoleSpy).toHaveBeenCalledWith(
      "Unsaved changes:",
      expect.objectContaining({
        hasUnsavedChanges: true,
      })
    );

    consoleSpy.mockRestore();
  });

  it("should not save initial empty state", () => {
    render(
      <ApprovalProvider initialPosts={[]}>
        <TestComponent />
      </ApprovalProvider>
    );

    // Should not call save for empty initial state
    expect(storage.debouncedSaveApprovalState).not.toHaveBeenCalled();
  });

  it("should handle beforeunload event for unsaved changes", () => {
    storage.checkForUnsavedChanges.mockReturnValue({
      hasUnsavedChanges: true,
      unsavedPosts: [],
      message: "Unsaved changes detected",
    });

    const addEventListenerSpy = vi.spyOn(window, "addEventListener");
    const removeEventListenerSpy = vi.spyOn(window, "removeEventListener");

    const { unmount } = render(
      <ApprovalProvider initialPosts={mockInitialPosts}>
        <TestComponent />
      </ApprovalProvider>
    );

    // Should add beforeunload listener
    expect(addEventListenerSpy).toHaveBeenCalledWith(
      "beforeunload",
      expect.any(Function)
    );

    // Should remove listener on unmount
    unmount();
    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      "beforeunload",
      expect.any(Function)
    );

    addEventListenerSpy.mockRestore();
    removeEventListenerSpy.mockRestore();
  });
});
