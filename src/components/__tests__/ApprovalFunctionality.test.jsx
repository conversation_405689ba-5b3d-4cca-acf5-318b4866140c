import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Post from '../Post';
import PostActions from '../PostActions';
import { ApprovalProvider, useApproval } from '../ApprovalProvider';
import { ApprovalSummary } from '../ApprovalSummary';

// Mock the UI components
vi.mock('../ui/card', () => ({
  Card: ({ children, className }) => <div className={className}>{children}</div>,
  CardContent: ({ children, className }) => <div className={className}>{children}</div>
}));

vi.mock('../ui/button', () => ({
  Button: ({ children, onClick, className, disabled, ...props }) => (
    <button 
      onClick={onClick} 
      className={className} 
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  )
}));

vi.mock('../ui/tooltip', () => ({
  Tooltip: ({ children }) => <div>{children}</div>,
  TooltipTrigger: ({ children, asChild }) => asChild ? children : <div>{children}</div>,
  TooltipContent: ({ children }) => <div>{children}</div>
}));

vi.mock('../ui/select', () => ({
  Select: ({ children, onValueChange, value }) => (
    <select onChange={(e) => onValueChange?.(e.target.value)} value={value}>
      {children}
    </select>
  ),
  SelectContent: ({ children }) => <div>{children}</div>,
  SelectItem: ({ children, value }) => <option value={value}>{children}</option>,
  SelectTrigger: ({ children }) => <div>{children}</div>,
  SelectValue: ({ placeholder }) => <span>{placeholder}</span>
}));

vi.mock('../ui/progress', () => ({
  Progress: ({ value, className }) => (
    <div className={className} data-testid="progress-bar" data-value={value}>
      <div style={{ width: `${value}%` }}></div>
    </div>
  )
}));

vi.mock('../ui/badge', () => ({
  Badge: ({ children, className }) => <span className={className}>{children}</span>
}));

// Mock utils
vi.mock('../../lib/utils', () => ({
  cn: (...classes) => classes.filter(Boolean).join(' ')
}));

// Mock Lucide icons
vi.mock('lucide-react', () => ({
  CheckIcon: ({ className }) => <div className={className} data-testid="check-icon">✓</div>,
  XIcon: ({ className }) => <div className={className} data-testid="x-icon">✗</div>,
  ChevronDownIcon: ({ className }) => <div className={className}>▼</div>
}));

// Mock NotesDialog
vi.mock('../NotesDialog', () => ({
  default: ({ open, onSave, notes }) => 
    open ? (
      <div data-testid="notes-dialog">
        <textarea defaultValue={notes} data-testid="notes-input" />
        <button onClick={() => onSave('test note')}>Save</button>
      </div>
    ) : null
}));

// Mock EditableInput and EditableTextarea
vi.mock('../EditableInput', () => ({
  EditableInput: ({ value, onSave }) => (
    <input 
      defaultValue={value} 
      onBlur={(e) => onSave?.(e.target.value)}
      data-testid="editable-input"
    />
  )
}));

vi.mock('../EditableTextarea', () => ({
  EditableTextarea: ({ value, onSave }) => (
    <textarea 
      defaultValue={value} 
      onBlur={(e) => onSave?.(e.target.value)}
      data-testid="editable-textarea"
    />
  )
}));

// Test data
const mockPost = {
  id: '1',
  title: 'Test Post',
  caption: 'Test caption',
  hashtags: '#test #post',
  image: '/test-image.jpg',
  category: 'test',
  originalContent: {
    title: 'Test Post',
    caption: 'Test caption',
    hashtags: '#test #post'
  },
  isFavorited: false,
  notes: '',
  isEdited: false,
  editedContent: {},
  approvalStatus: 'pending',
  lastModified: new Date(),
  displayContent: {
    title: 'Test Post',
    caption: 'Test caption',
    hashtags: '#test #post'
  },
  hasNotes: false,
  hasChanges: false,
  isReviewed: false
};

// Test component that uses the approval context
function TestComponent() {
  const approval = useApproval();

  return (
    <div>
      <div data-testid="approval-status">{approval.posts['1']?.approvalStatus || 'pending'}</div>
      <div data-testid="approved-count">{approval.summary.approvedCount || 0}</div>
      <div data-testid="declined-count">{approval.summary.declinedCount || 0}</div>
      <div data-testid="reviewed-count">{approval.summary.reviewedCount || 0}</div>
      <button
        data-testid="approve-post"
        onClick={() => approval.setApprovalStatus('1', 'approved')}
      >
        Approve
      </button>
      <button
        data-testid="decline-post"
        onClick={() => approval.setApprovalStatus('1', 'declined')}
      >
        Decline
      </button>
    </div>
  );
}

describe('Approval Functionality', () => {
  describe('ApprovalProvider', () => {
    it('should handle SET_APPROVAL_STATUS action correctly', async () => {
      render(
        <ApprovalProvider initialPosts={[mockPost]}>
          <TestComponent />
        </ApprovalProvider>
      );

      // Initial state should be pending
      expect(screen.getByTestId('approval-status')).toHaveTextContent('pending');
      expect(screen.getByTestId('approved-count')).toHaveTextContent('0');
      expect(screen.getByTestId('declined-count')).toHaveTextContent('0');
      expect(screen.getByTestId('reviewed-count')).toHaveTextContent('0');

      // Approve the post
      fireEvent.click(screen.getByTestId('approve-post'));

      await waitFor(() => {
        expect(screen.getByTestId('approval-status')).toHaveTextContent('approved');
        expect(screen.getByTestId('approved-count')).toHaveTextContent('1');
        expect(screen.getByTestId('reviewed-count')).toHaveTextContent('1');
      });

      // Decline the post
      fireEvent.click(screen.getByTestId('decline-post'));

      await waitFor(() => {
        expect(screen.getByTestId('approval-status')).toHaveTextContent('declined');
        expect(screen.getByTestId('approved-count')).toHaveTextContent('0');
        expect(screen.getByTestId('declined-count')).toHaveTextContent('1');
        expect(screen.getByTestId('reviewed-count')).toHaveTextContent('1');
      });
    });
  });

  describe('PostActions Component', () => {
    it('should render approve and decline buttons', () => {
      const mockHandlers = {
        onToggleFavorite: vi.fn(),
        onUpdateNotes: vi.fn(),
        onSetApprovalStatus: vi.fn()
      };

      render(
        <PostActions 
          post={mockPost} 
          {...mockHandlers}
        />
      );

      expect(screen.getByTestId('check-icon')).toBeInTheDocument();
      expect(screen.getByTestId('x-icon')).toBeInTheDocument();
    });

    it('should call onSetApprovalStatus when approve button is clicked', () => {
      const mockSetApprovalStatus = vi.fn();
      const mockHandlers = {
        onToggleFavorite: vi.fn(),
        onUpdateNotes: vi.fn(),
        onSetApprovalStatus: mockSetApprovalStatus
      };

      render(
        <PostActions 
          post={mockPost} 
          {...mockHandlers}
        />
      );

      const approveButton = screen.getByTestId('check-icon').closest('button');
      fireEvent.click(approveButton);

      expect(mockSetApprovalStatus).toHaveBeenCalledWith('1', 'approved');
    });

    it('should call onSetApprovalStatus when decline button is clicked', () => {
      const mockSetApprovalStatus = vi.fn();
      const mockHandlers = {
        onToggleFavorite: vi.fn(),
        onUpdateNotes: vi.fn(),
        onSetApprovalStatus: mockSetApprovalStatus
      };

      render(
        <PostActions 
          post={mockPost} 
          {...mockHandlers}
        />
      );

      const declineButton = screen.getByTestId('x-icon').closest('button');
      fireEvent.click(declineButton);

      expect(mockSetApprovalStatus).toHaveBeenCalledWith('1', 'declined');
    });

    it('should toggle approval status when buttons are clicked multiple times', () => {
      const mockSetApprovalStatus = vi.fn();
      const approvedPost = { ...mockPost, approvalStatus: 'approved' };
      const mockHandlers = {
        onToggleFavorite: vi.fn(),
        onUpdateNotes: vi.fn(),
        onSetApprovalStatus: mockSetApprovalStatus
      };

      render(
        <PostActions 
          post={approvedPost} 
          {...mockHandlers}
        />
      );

      const approveButton = screen.getByTestId('check-icon').closest('button');
      fireEvent.click(approveButton);

      // Should toggle back to pending when already approved
      expect(mockSetApprovalStatus).toHaveBeenCalledWith('1', 'pending');
    });
  });

  describe('Progress Calculation', () => {
    it('should calculate progress based only on reviewed posts (approved + declined)', async () => {
      const posts = [
        { ...mockPost, id: '1', approvalStatus: 'approved' },
        { ...mockPost, id: '2', approvalStatus: 'declined' },
        { ...mockPost, id: '3', approvalStatus: 'pending', isFavorited: true, hasNotes: false },
        { ...mockPost, id: '4', approvalStatus: 'pending', notes: 'test note', hasNotes: true }
      ];

      render(
        <ApprovalProvider initialPosts={posts}>
          <ApprovalSummary />
        </ApprovalProvider>
      );

      // Progress should be 50% (2 reviewed out of 4 total)
      await waitFor(() => {
        const progressBar = screen.getByTestId('progress-bar');
        expect(progressBar).toHaveAttribute('data-value', '50');
      });

      // Should show "2 de 4 revisados (50%)"
      expect(screen.getByText(/2 de 4 revisados \(50%\)/)).toBeInTheDocument();
    });
  });
});
