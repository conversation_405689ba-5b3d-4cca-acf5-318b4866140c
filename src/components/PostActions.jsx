import React, { useState } from 'react';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { cn } from '../lib/utils';
import { CheckIcon, XIcon } from 'lucide-react';
import NotesDialog from './NotesDialog';

/**
 * PostActions component - Floating action buttons for post interactions
 * @param {Object} props
 * @param {import('../types/index.js').EnhancedPost} props.post
 * @param {Function} props.onToggleFavorite
 * @param {Function} props.onUpdateNotes
 * @param {Function} props.onSetApprovalStatus
 * @param {boolean} props.disabled
 */
function PostActions({
  post,
  onToggleFavorite,
  onUpdateNotes,
  onSetApprovalStatus,
  disabled = false
}) {
  const [showNotesDialog, setShowNotesDialog] = useState(false);

  const handleFavoriteClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled && onToggleFavorite) {
      // Add bounce animation class temporarily
      const button = e.currentTarget;
      button.classList.add('animate-bounce-gentle');
      setTimeout(() => {
        button.classList.remove('animate-bounce-gentle');
      }, 300);
      
      onToggleFavorite(post.id);
    }
  };

  const handleNotesClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setShowNotesDialog(true);
    }
  };

  const handleSaveNotes = (notes) => {
    if (onUpdateNotes) {
      onUpdateNotes(post.id, notes);
    }
  };

  const handleApproveClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled && onSetApprovalStatus) {
      const newStatus = post.approvalStatus === 'approved' ? 'pending' : 'approved';
      onSetApprovalStatus(post.id, newStatus);
    }
  };

  const handleDeclineClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled && onSetApprovalStatus) {
      const newStatus = post.approvalStatus === 'declined' ? 'pending' : 'declined';
      onSetApprovalStatus(post.id, newStatus);
    }
  };

  return (
    <>
      <div className="absolute bottom-3 right-3 flex gap-2 z-10">
        {/* Approve Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleApproveClick}
              disabled={disabled}
              className={cn(
                "h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm shadow-sm",
                "hover:bg-white hover:shadow-md transition-all duration-200",
                "hover:scale-105 active:scale-95",
                "border border-gray-200/50",
                post.approvalStatus === 'approved' && "bg-green-50/90 hover:bg-green-50"
              )}
              aria-label={post.approvalStatus === 'approved' ? "Remove approval" : "Approve post"}
            >
              <CheckIcon
                className={cn(
                  "h-4 w-4 transition-all duration-200",
                  post.approvalStatus === 'approved'
                    ? "text-green-600"
                    : "text-gray-500 hover:text-green-500"
                )}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {post.approvalStatus === 'approved' ? "Remove approval" : "Approve post"}
          </TooltipContent>
        </Tooltip>

        {/* Decline Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleDeclineClick}
              disabled={disabled}
              className={cn(
                "h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm shadow-sm",
                "hover:bg-white hover:shadow-md transition-all duration-200",
                "hover:scale-105 active:scale-95",
                "border border-gray-200/50",
                post.approvalStatus === 'declined' && "bg-red-50/90 hover:bg-red-50"
              )}
              aria-label={post.approvalStatus === 'declined' ? "Remove decline" : "Decline post"}
            >
              <XIcon
                className={cn(
                  "h-4 w-4 transition-all duration-200",
                  post.approvalStatus === 'declined'
                    ? "text-red-600"
                    : "text-gray-500 hover:text-red-500"
                )}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {post.approvalStatus === 'declined' ? "Remove decline" : "Decline post"}
          </TooltipContent>
        </Tooltip>

        {/* Favorite/Heart Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleFavoriteClick}
              disabled={disabled}
              className={cn(
                "h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm shadow-sm",
                "hover:bg-white hover:shadow-md transition-all duration-200",
                "hover:scale-105 active:scale-95",
                "border border-gray-200/50",
                post.isFavorited && "bg-red-50/90 hover:bg-red-50"
              )}
              aria-label={post.isFavorited ? "Remove from favorites" : "Add to favorites"}
            >
              <HeartIcon
                className={cn(
                  "h-4 w-4 transition-all duration-200",
                  post.isFavorited
                    ? "text-red-500 fill-red-500"
                    : "text-gray-500 hover:text-red-400"
                )}
                filled={post.isFavorited}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {post.isFavorited ? "Remove from favorites" : "Add to favorites"}
          </TooltipContent>
        </Tooltip>

        {/* Notes Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNotesClick}
              disabled={disabled}
              className={cn(
                "h-8 w-8 rounded-full bg-white/90 backdrop-blur-sm shadow-sm",
                "hover:bg-white hover:shadow-md transition-all duration-200",
                "hover:scale-105 active:scale-95",
                "border border-gray-200/50",
                post.notes.trim().length > 0 && "bg-orange-50/90 hover:bg-orange-50"
              )}
              aria-label={post.notes.trim().length > 0 ? "Edit note" : "Add note"}
            >
              <NotesIcon 
                className={cn(
                  "h-4 w-4 transition-all duration-200",
                  post.notes.trim().length > 0 
                    ? "text-orange-500" 
                    : "text-gray-500 hover:text-orange-400"
                )}
                hasNotes={post.notes.trim().length > 0}
              />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {post.notes.trim().length > 0 
              ? (
                <div className="max-w-xs">
                  <div className="font-medium mb-1">Note:</div>
                  <div className="text-xs">{post.notes}</div>
                </div>
              )
              : "Add note"
            }
          </TooltipContent>
        </Tooltip>
      </div>

      {/* Notes Dialog */}
      <NotesDialog
        open={showNotesDialog}
        onOpenChange={setShowNotesDialog}
        notes={post.notes}
        onSave={handleSaveNotes}
        postTitle={post.title}
      />
    </>
  );
}

/**
 * Heart Icon Component
 * @param {Object} props
 * @param {string} props.className
 * @param {boolean} props.filled
 */
function HeartIcon({ className, filled = false }) {
  return (
    <svg
      className={className}
      fill={filled ? "currentColor" : "none"}
      stroke="currentColor"
      strokeWidth="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z"
      />
    </svg>
  );
}

/**
 * Notes Icon Component
 * @param {Object} props
 * @param {string} props.className
 * @param {boolean} props.hasNotes
 */
function NotesIcon({ className, hasNotes = false }) {
  return (
    <svg
      className={className}
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
      />
      {hasNotes && (
        <circle
          cx="18"
          cy="6"
          r="3"
          fill="currentColor"
          className="text-orange-500"
        />
      )}
    </svg>
  );
}

export default PostActions;