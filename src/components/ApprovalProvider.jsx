import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { 
  loadApprovalState, 
  debouncedSaveApprovalState, 
  isStorageAvailable,
  checkForUnsavedChanges 
} from '../lib/storage.js';
import { transformToEnhancedPost } from '../lib/postUtils.js';

/**
 * @typedef {import('../types/index.js').AppState} AppState
 * @typedef {import('../types/index.js').ApprovalActions} ApprovalActions
 * @typedef {import('../types/index.js').EnhancedPost} EnhancedPost
 * @typedef {import('../types/index.js').PostContent} PostContent
 * @typedef {import('../types/index.js').FilterType} FilterType
 */
 
const ApprovalContext = createContext(null);

// Action types
export const ACTIONS = {
  UPDATE_POST: 'UPDATE_POST',
  TOGGLE_FAVORITE: 'TOGGLE_FAVORITE',
  UPDATE_NOTES: 'UPDATE_NOTES',
  SET_APPROVAL_STATUS: 'SET_APPROVAL_STATUS',
  SET_FILTER: 'SET_FILTER',
  SET_EDITING: 'SET_EDITING',
  SET_VIEW_MODE: 'SET_VIEW_MODE',
  SET_ADMIN_AUTHENTICATED: 'SET_ADMIN_AUTHENTICATED',
  SET_SHOW_PASSCODE_DIALOG: 'SET_SHOW_PASSCODE_DIALOG',
  LOAD_STATE: 'LOAD_STATE',
  UPDATE_SUMMARY: 'UPDATE_SUMMARY'
};

/**
 * Initial state for the approval system
 * @type {AppState}
 */
const initialState = {
  posts: {},
  ui: {
    editingPostId: null,
    filter: 'all',
    isLoading: false,
    lastSaved: null,
    viewMode: 'client',
    isAdminAuthenticated: false,
    showPasscodeDialog: false
  },
  summary: {
    totalPosts: 0,
    favoritedCount: 0,
    notesCount: 0,
    editedCount: 0,
    approvedCount: 0,
    declinedCount: 0,
    reviewedCount: 0
  }
};

/**
 * Calculate summary statistics from posts
 * @param {Record<string, EnhancedPost>} posts 
 * @returns {import('../types/index.js').SummaryData}
 */
function calculateSummary(posts) {
  const postsArray = Object.values(posts);

  const approvedCount = postsArray.filter(post => post.approvalStatus === 'approved').length;
  const declinedCount = postsArray.filter(post => post.approvalStatus === 'declined').length;

  return {
    totalPosts: postsArray.length,
    favoritedCount: postsArray.filter(post => post.isFavorited).length,
    notesCount: postsArray.filter(post => post.hasNotes).length,
    editedCount: postsArray.filter(post => post.hasChanges).length,
    approvedCount,
    declinedCount,
    reviewedCount: approvedCount + declinedCount
  };
}

/**
 * Approval state reducer
 * @param {AppState} state 
 * @param {Object} action 
 * @returns {AppState}
 */
function approvalReducer(state, action) {
  switch (action.type) {
    case ACTIONS.UPDATE_POST: {
      const { postId, updates } = action.payload;
      const updatedPosts = {
        ...state.posts,
        [postId]: {
          ...state.posts[postId],
          editedContent: {
            ...state.posts[postId].editedContent,
            ...updates
          },
          isEdited: true,
          lastModified: new Date()
        }
      };
      
      return {
        ...state,
        posts: updatedPosts,
        summary: calculateSummary(updatedPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date()
        }
      };
    }

    case ACTIONS.TOGGLE_FAVORITE: {
      const { postId } = action.payload;
      const updatedPosts = {
        ...state.posts,
        [postId]: {
          ...state.posts[postId],
          isFavorited: !state.posts[postId].isFavorited,
          lastModified: new Date()
        }
      };

      return {
        ...state,
        posts: updatedPosts,
        summary: calculateSummary(updatedPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date()
        }
      };
    }

    case ACTIONS.UPDATE_NOTES: {
      const { postId, notes } = action.payload;
      const updatedPosts = {
        ...state.posts,
        [postId]: {
          ...state.posts[postId],
          notes,
          lastModified: new Date()
        }
      };

      return {
        ...state,
        posts: updatedPosts,
        summary: calculateSummary(updatedPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date()
        }
      };
    }

    case ACTIONS.SET_APPROVAL_STATUS: {
      const { postId, approvalStatus } = action.payload;
      const updatedPosts = {
        ...state.posts,
        [postId]: {
          ...state.posts[postId],
          approvalStatus,
          lastModified: new Date()
        }
      };

      return {
        ...state,
        posts: updatedPosts,
        summary: calculateSummary(updatedPosts),
        ui: {
          ...state.ui,
          lastSaved: new Date()
        }
      };
    }

    case ACTIONS.SET_FILTER:
      return {
        ...state,
        ui: {
          ...state.ui,
          filter: action.payload
        }
      };

    case ACTIONS.SET_EDITING:
      return {
        ...state,
        ui: {
          ...state.ui,
          editingPostId: action.payload
        }
      };

    case ACTIONS.SET_VIEW_MODE:
      return {
        ...state,
        ui: {
          ...state.ui,
          viewMode: action.payload
        }
      };

    case ACTIONS.SET_ADMIN_AUTHENTICATED:
      return {
        ...state,
        ui: {
          ...state.ui,
          isAdminAuthenticated: action.payload
        }
      };

    case ACTIONS.SET_SHOW_PASSCODE_DIALOG:
      return {
        ...state,
        ui: {
          ...state.ui,
          showPasscodeDialog: action.payload
        }
      };

    case ACTIONS.LOAD_STATE:
      return {
        ...action.payload,
        summary: calculateSummary(action.payload.posts)
      };

    case ACTIONS.UPDATE_SUMMARY:
      return {
        ...state,
        summary: calculateSummary(state.posts)
      };

    default:
      return state;
  }
}

/**
 * ApprovalProvider component
 * @param {Object} props
 * @param {React.ReactNode} props.children
 * @param {Array} props.initialPosts - Initial posts data
 */
export function ApprovalProvider({ children, initialPosts = [] }) {
  const [state, dispatch] = useReducer(approvalReducer, initialState);

  // Load saved state on mount
  useEffect(() => {
    const storageAvailable = isStorageAvailable();
    
    if (!storageAvailable) {
      console.warn('localStorage is not available, persistence disabled');
    }

    const savedState = storageAvailable ? loadApprovalState() : null;
    
    if (savedState) {
      // Merge saved state with initial posts if needed
      const mergedPosts = { ...savedState.posts };
      
      // Add any new posts from initialPosts that aren't in saved state
      initialPosts.forEach(post => {
        if (!mergedPosts[post.id]) {
          mergedPosts[post.id] = {
            ...post,
            isFavorited: post.isFavorited || false,
            notes: post.notes || '',
            isEdited: post.isEdited || false,
            editedContent: post.editedContent || {},
            approvalStatus: post.approvalStatus || 'pending',
            lastModified: post.lastModified || new Date()
          };
        }
      });

      dispatch({
        type: ACTIONS.LOAD_STATE,
        payload: {
          ...savedState,
          posts: mergedPosts
        }
      });
    } else if (initialPosts.length > 0) {
      // No saved state or storage unavailable, initialize with initial posts
      const enhancedPosts = {};
      initialPosts.forEach(post => {
        const enhancedPost = transformToEnhancedPost(post);
        enhancedPosts[enhancedPost.id] = enhancedPost;
      });

      dispatch({
        type: ACTIONS.LOAD_STATE,
        payload: {
          ...initialState,
          posts: enhancedPosts
        }
      });
    }
  }, []); // Only run on mount - initialPosts should be static

  // Auto-save state changes with debouncing
  useEffect(() => {
    if (!isStorageAvailable()) return;

    // Don't save initial empty state
    if (Object.keys(state.posts).length === 0) return;

    // Use debounced save to prevent excessive localStorage writes
    debouncedSaveApprovalState(state);
  }, [state]);

  // Check for unsaved changes on page unload
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (!isStorageAvailable()) return;
      
      const unsavedChanges = checkForUnsavedChanges(state);
      
      if (unsavedChanges.hasUnsavedChanges) {
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [state]);

  // Actions
  const actions = {
    updatePost: (postId, updates) => {
      dispatch({
        type: ACTIONS.UPDATE_POST,
        payload: { postId, updates }
      });
    },

    toggleFavorite: (postId) => {
      dispatch({
        type: ACTIONS.TOGGLE_FAVORITE,
        payload: { postId }
      });
    },

    updateNotes: (postId, notes) => {
      dispatch({
        type: ACTIONS.UPDATE_NOTES,
        payload: { postId, notes }
      });
    },

    setApprovalStatus: (postId, approvalStatus) => {
      dispatch({
        type: ACTIONS.SET_APPROVAL_STATUS,
        payload: { postId, approvalStatus }
      });
    },

    setFilter: (filter) => {
      dispatch({
        type: ACTIONS.SET_FILTER,
        payload: filter
      });
    },

    setEditing: (postId) => {
      dispatch({
        type: ACTIONS.SET_EDITING,
        payload: postId
      });
    },

    setViewMode: (viewMode) => {
      dispatch({
        type: ACTIONS.SET_VIEW_MODE,
        payload: viewMode
      });
    },

    setAdminAuthenticated: (isAuthenticated) => {
      dispatch({
        type: ACTIONS.SET_ADMIN_AUTHENTICATED,
        payload: isAuthenticated
      });
    },

    setShowPasscodeDialog: (show) => {
      dispatch({
        type: ACTIONS.SET_SHOW_PASSCODE_DIALOG,
        payload: show
      });
    },

    // Persistence utilities
    loadState: (loadedState) => {
      dispatch({
        type: ACTIONS.LOAD_STATE,
        payload: loadedState
      });
    },

    checkUnsavedChanges: () => {
      if (!isStorageAvailable()) {
        return { hasUnsavedChanges: false, message: 'Storage not available' };
      }
      return checkForUnsavedChanges(state);
    },

    forceSync: () => {
      if (isStorageAvailable()) {
        debouncedSaveApprovalState(state);
      }
    }
  };

  const value = {
    ...state,
    ...actions
  };

  return (
    <ApprovalContext.Provider value={value}>
      {children}
    </ApprovalContext.Provider>
  );
}

/**
 * Hook to use approval context
 * @returns {AppState & ApprovalActions}
 */
export function useApproval() {
  const context = useContext(ApprovalContext);
  if (!context) {
    throw new Error('useApproval must be used within an ApprovalProvider');
  }
  return context;
}