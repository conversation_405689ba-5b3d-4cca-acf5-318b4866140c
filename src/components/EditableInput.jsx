import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';

/**
 * EditableInput component for inline editing functionality
 * @param {Object} props
 * @param {string} props.value - Current value of the input
 * @param {function} props.onSave - Callback when value is saved
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Whether the input is disabled
 * @param {number} props.maxLength - Maximum character length
 */
export function EditableInput({ 
  value = '', 
  onSave, 
  placeholder = 'Click to edit...', 
  className = '',
  disabled = false,
  maxLength = 100
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaved, setIsSaved] = useState(false);

  const inputRef = useRef(null);

  // Update editValue when value prop changes
  useEffect(() => {
    setEditValue(value);
  }, [value]);

  // Focus input when entering edit mode
  useEffect(() => {
    if (isEditing && inputRef.current) {
      // Use setTimeout to ensure the input is rendered
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();
        }
      }, 0);
    }
  }, [isEditing]);

  const handleClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  const handleSave = () => {
    if (editValue !== value) {
      onSave?.(editValue);
      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 1000); // Show saved state for 1 second
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(value); // Reset to original value
    setIsEditing(false);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancel();
    }
  };

  const handleBlur = () => {
    handleSave();
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    if (!maxLength || newValue.length <= maxLength) {
      setEditValue(newValue);
    } else {
      // Prevent the change by keeping the current value
      e.target.value = editValue;
    }
  };

  if (isEditing) {
    return (
      <div className={cn("relative", className)}>
        <input
          ref={inputRef}
          type="text"
          value={editValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          maxLength={maxLength}
          className={cn(
            "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
            "focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
            "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
            "border-blue-500 ring-blue-500/20 ring-2"
          )}
        />
        {maxLength && (
          <div className="absolute -bottom-5 right-0 text-xs text-gray-500">
            {editValue.length}/{maxLength}
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      onClick={handleClick}
      className={cn(
        "min-h-[36px] px-3 py-2 rounded-md border border-transparent cursor-pointer transition-all duration-200",
        "hover:border-gray-200 hover:bg-gray-50",
        "focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500",
        disabled && "cursor-not-allowed opacity-50",
        isSaved && "border-green-500 bg-green-50",
        value !== editValue && "border-blue-200 bg-blue-50", // Show edited state
        className
      )}
      tabIndex={disabled ? -1 : 0}
      role="button"
      aria-label={`Click to edit ${placeholder}`}
    >
      <span className={cn(
        "block text-sm",
        !value && "text-gray-400 italic"
      )}>
        {value || placeholder}
      </span>
      {isSaved && (
        <div className="absolute -top-2 -right-2 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
      )}
    </div>
  );
}