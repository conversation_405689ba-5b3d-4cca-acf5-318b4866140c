import React from 'react';
import { posts } from '../data/posts.js';
import { ApprovalProvider, useApproval } from './ApprovalProvider.jsx';
import { ApprovalSummary } from './ApprovalSummary.jsx';
import EnhancedPostCard from './EnhancedPostCard.jsx';
import { filterPosts } from '../lib/postUtils.js';
import '../styles/App.css';

/**
 * Main App Content component that uses the approval context
 */
function AppContent() {
  const { posts: enhancedPosts, ui } = useApproval();

  // Filter posts based on current filter
  const filteredPosts = filterPosts(enhancedPosts, ui.filter);

  return (
    <div className="app">
      <header className="header">
        <h1>EliteDecor</h1>
        <p>Mobiliário Personalizado de Luxo</p>
      </header>
      
      <main className="main">
        {/* Approval Summary Component */}
        <ApprovalSummary />
        
        {/* Posts Grid with Filtering */}
        <div className="posts-grid-container">
          {filteredPosts.length > 0 ? (
            <div className="posts-grid">
              {filteredPosts.map((post) => (
                <div 
                  key={post.id} 
                  className="post-grid-item animate-fade-in"
                  style={{
                    animationDelay: `${filteredPosts.indexOf(post) * 50}ms`
                  }}
                >
                  <EnhancedPostCard post={post} />
                </div>
              ))}
            </div>
          ) : (
            <EmptyState filter={ui.filter} />
          )}
        </div>
      </main>
      
      <footer className="footer">
        <div className="contact-info">
          <h3>Contacte-nos</h3>
          <p>Email: <EMAIL></p>
          <p>Website: <a href="https://elitedecor.pt" target="_blank" rel="noopener noreferrer">elitedecor.pt</a></p>
        </div>
      </footer>
    </div>
  );
}

/**
 * Empty state component for when no posts match the current filter
 * @param {Object} props
 * @param {import('../types/index.js').FilterType} props.filter
 */
function EmptyState({ filter }) {
  const getEmptyStateMessage = () => {
    switch (filter) {
      case 'favorited':
        return {
          title: 'Nenhum post favorito',
          description: 'Marque posts como favoritos clicando no ícone de coração para vê-los aqui.',
          icon: '❤️'
        };
      case 'with-notes':
        return {
          title: 'Nenhum post com notas',
          description: 'Adicione notas aos posts clicando no ícone de notas para vê-los aqui.',
          icon: '📝'
        };
      case 'unreviewed':
        return {
          title: 'Nenhum post não revisado',
          description: 'Todos os posts foram revisados! Parabéns pelo progresso.',
          icon: '✅'
        };
      default:
        return {
          title: 'Nenhum post encontrado',
          description: 'Não há posts disponíveis no momento.',
          icon: '📋'
        };
    }
  };

  const { title, description, icon } = getEmptyStateMessage();

  return (
    <div className="empty-state">
      <div className="empty-state-content">
        <div className="empty-state-icon">{icon}</div>
        <h3 className="empty-state-title">{title}</h3>
        <p className="empty-state-description">{description}</p>
      </div>
    </div>
  );
}

/**
 * Main App component wrapped with ApprovalProvider
 */
function App() {
  return (
    <ApprovalProvider initialPosts={posts}>
      <AppContent />
    </ApprovalProvider>
  );
}

export default App;
